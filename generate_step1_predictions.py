# -*- coding: utf-8 -*-

"""
从训练好的Step1模型生成预测结果，用于Step2训练
使用方法：
python generate_step1_predictions.py --step1_model_path ./checkpoints/step1/best_model.pt --dataset iemocap --output_dir ./step1_predictions
"""

import torch
import json
import os
import argparse
from tqdm import tqdm

from config import Config, get_model_config
from data_loader import create_data_loaders
from models import MECPE_Step1_Model


def generate_predictions(model, dataloader, device):
    """生成Step1预测结果"""
    model.eval()
    predictions = {}

    with torch.no_grad():
        for batch in tqdm(dataloader, desc="生成预测"):
            # 移动数据到设备
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            audio_features = batch['audio_features'].to(device)
            visual_features = batch['visual_features'].to(device)
            doc_len = batch['doc_len'].to(device)
            conversation_ids = batch['conversation_id']
            texts = batch.get('texts', None)  # 原始文本（BERT_doc需要）
            speakers = batch.get('speakers', None)  # 说话人信息（GAT需要）

            # 前向传播
            outputs = model(input_ids, attention_mask, audio_features, visual_features, doc_len, texts, speakers)

            # 获取预测结果
            emotion_preds = torch.argmax(outputs['emotion_logits'], dim=-1)  # (batch_size, seq_len)
            cause_preds = torch.argmax(outputs['cause_logits'], dim=-1)     # (batch_size, seq_len)

            # 处理每个对话
            for i, conv_id in enumerate(conversation_ids):
                actual_len = doc_len[i].item()

                # 提取有效长度的预测
                pred_emo = emotion_preds[i][:actual_len].cpu().tolist()
                pred_cause = cause_preds[i][:actual_len].cpu().tolist()

                # 根据模型类型处理情感预测
                if outputs['emotion_logits'].size(-1) == 2:
                    # 二分类模式：直接使用预测结果
                    pred_emo_binary = pred_emo
                    pred_emocate = pred_emo  # 二分类情况下，emocate与binary相同
                else:
                    # 多类模式：转换为二分类 + 保存原始类别
                    pred_emo_binary = [1 if x > 0 else 0 for x in pred_emo]
                    pred_emocate = pred_emo  # 保存原始多类预测

                pred_cause_binary = pred_cause  # 原因检测本身就是二分类

                # 标准化对话ID为字符串，确保JSON序列化与后续加载匹配
                if isinstance(conv_id, bytes):
                    conv_id_str = conv_id.decode('utf-8')
                else:
                    conv_id_str = str(conv_id)

                predictions[conv_id_str] = {
                    'pred_emo': pred_emo_binary,
                    'pred_cause': pred_cause_binary,
                    'pred_emocate': pred_emocate
                }

    return predictions


def main():
    parser = argparse.ArgumentParser(description='生成Step1预测结果用于Step2')
    parser.add_argument('--step1_model_path', type=str, required=True,
                       help='Step1模型路径')
    parser.add_argument('--dataset', type=str, default='iemocap',
                       choices=['iemocap', 'meld'], help='数据集名称')
    parser.add_argument('--model_type', type=str, default='bilstm',
                       choices=['bilstm', 'bert'], help='Step1模型类型（需与训练时一致）')
    parser.add_argument('--use_emocate', action='store_true', default=False,
                       help='是否使用细粒度情感分类（需与训练时一致）')
    parser.add_argument('--bert_encoding_type', type=str, default='bert_sen',
                       choices=['bert_sen', 'bert_doc'],
                       help='BERT编码方式（需与训练时一致）')
    parser.add_argument('--output_dir', type=str, default='./step1_predictions',
                       help='输出目录')
    parser.add_argument('--batch_size', type=int, default=8,
                       help='批次大小')
    parser.add_argument('--device', type=str, default='cuda',
                       help='设备')
    
    args = parser.parse_args()
    
    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    print(f"加载Step1模型: {args.step1_model_path}")
    print(f"数据集: {args.dataset}")
    print(f"设备: {device}")
    
    # 创建数据加载器
    print("加载数据...")
    train_loader, test_loader, dev_loader, vocab_size = create_data_loaders(
        dataset_name=args.dataset,
        batch_size=args.batch_size,
        stage='step1',
        use_emocate=args.use_emocate
    )
    
    # 创建模型配置（需要与训练时一致）
    # 这里使用默认配置，实际使用时可能需要从检查点中加载配置
    config_args = argparse.Namespace()
    config_args.dataset = args.dataset
    config_args.model_type = args.model_type  # 与训练时一致
    config_args.use_emocate = args.use_emocate  # 与训练时一致
    config_args.bert_encoding_type = args.bert_encoding_type  # 与训练时一致
    config_args.hidden_dim = 200
    config_args.n_heads = 8
    config_args.dropout = 0.1
    config_args.share_encoder = True
    config_args.vocab_size = vocab_size
    config_args.embedding_dim = 300
    config_args.max_doc_len = 35
    config_args.max_sen_len = 35
    config_args.use_audio = True
    config_args.use_visual = True
    config_args.bert_model_name = '../roberta'

    # 数据集特定配置
    if args.dataset == 'iemocap':
        config_args.audio_dim = 100
        config_args.visual_dim = 100
        if args.use_emocate:
            config_args.n_emotions = 6  # 细粒度情感分类
        else:
            config_args.n_emotions = 2  # 二分类：neutral vs non-neutral
    else:  # meld
        config_args.audio_dim = 6373
        config_args.visual_dim = 4096
        if args.use_emocate:
            config_args.n_emotions = 7  # 细粒度情感分类
        else:
            config_args.n_emotions = 2  # 二分类：neutral vs non-neutral
    
    # 创建模型
    model_config = get_model_config(config_args, 'step1')
    model = MECPE_Step1_Model(model_config).to(device)
    
    # 加载模型权重
    checkpoint = torch.load(args.step1_model_path, map_location=device)
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)
    
    print("模型加载完成")
    
    # 生成预测结果
    splits = [('train', train_loader), ('test', test_loader)]
    if dev_loader is not None:
        splits.append(('dev', dev_loader))
    
    for split_name, dataloader in splits:
        print(f"\n生成{split_name}集预测...")
        predictions = generate_predictions(model, dataloader, device)
        
        # 保存预测结果
        output_file = os.path.join(args.output_dir, f'{split_name}.json')
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(predictions, f, ensure_ascii=False, indent=2)
        
        print(f"预测结果已保存到: {output_file}")
        print(f"包含 {len(predictions)} 个对话的预测")
    
    print(f"\n所有预测结果已保存到: {args.output_dir}")
    print("\n使用方法:")
    print(f"python train_step2.py --use_predicted_labels --step1_pred_dir {args.output_dir} --dataset {args.dataset}")


if __name__ == "__main__":
    main()
