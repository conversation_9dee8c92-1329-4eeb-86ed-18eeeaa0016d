# -*- coding: utf-8 -*-

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import os
import sys
from tqdm import tqdm
import logging
from transformers import get_linear_schedule_with_warmup
from collections import defaultdict

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config, get_model_config
from data_loader import create_data_loaders
from models import MECPE_Step2_Model, Step2Loss, PairDataCollator
from utils import (
    set_seed, print_time, MetricsCalculator, Step2Evaluator,
    EarlyStopping, ModelSaver, list_round
)


def setup_logging(log_dir, dataset_name, model_type):
    """设置日志"""
    log_file = os.path.join(log_dir, f'step2_{dataset_name}_{model_type}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


def find_optimal_threshold(model, dataloader, device, logger, threshold_range=(0.3, 0.7), num_thresholds=21):
    """在验证集上搜索最优阈值"""
    model.eval()

    # 收集所有预测概率和真实标签
    all_probs = []
    all_labels = []
    all_conversation_ids = []
    all_emo_ids = []
    all_cause_ids = []

    logger.info("收集验证集预测概率...")
    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Collecting probabilities"):
            input_ids = batch['emo_input_ids'].to(device)
            attention_mask = batch['emo_attention_mask'].to(device)
            cause_input_ids = batch['cause_input_ids'].to(device)
            cause_attention_mask = batch['cause_attention_mask'].to(device)
            emo_audio = batch['emo_audio'].to(device)
            emo_visual = batch['emo_visual'].to(device)
            cause_audio = batch['cause_audio'].to(device)
            cause_visual = batch['cause_visual'].to(device)
            distance = batch['distance'].to(device)
            emotion_category = batch['emotion_category'].to(device)
            labels = batch['label'].to(device)
            conversation_ids = batch['conversation_id']
            emo_ids = batch['emo_id']
            cause_ids = batch['cause_id']

            # 前向传播
            outputs = model(
                input_ids, attention_mask, cause_input_ids, cause_attention_mask,
                emo_audio, emo_visual, cause_audio, cause_visual,
                distance, emotion_category
            )

            # 获取正类概率
            probs = torch.softmax(outputs['logits'], dim=-1)[:, 1]  # 正类概率

            all_probs.extend(probs.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            all_conversation_ids.extend(conversation_ids)
            all_emo_ids.extend(emo_ids)
            all_cause_ids.extend(cause_ids)

    # 转换为numpy数组
    all_probs = np.array(all_probs)
    all_labels = np.array(all_labels)

    # 搜索最优阈值
    thresholds = np.linspace(threshold_range[0], threshold_range[1], num_thresholds)
    best_threshold = 0.5
    best_f1 = 0.0

    logger.info(f"搜索最优阈值，范围: {threshold_range}, 步数: {num_thresholds}")

    for threshold in thresholds:
        # 使用当前阈值进行预测
        preds = (all_probs >= threshold).astype(int)

        # 计算对级别指标
        true_pairs_by_conv = defaultdict(list)
        pred_pairs_by_conv = defaultdict(list)

        for i, (conv_id, emo_id, cause_id, pred, label) in enumerate(
            zip(all_conversation_ids, all_emo_ids, all_cause_ids, preds, all_labels)
        ):
            if label == 1:  # 真实的正对
                true_pairs_by_conv[conv_id].append((conv_id, emo_id, cause_id))
            if pred == 1:  # 预测的正对
                pred_pairs_by_conv[conv_id].append((conv_id, emo_id, cause_id))

        # 获取所有对话ID
        all_conv_ids = set(true_pairs_by_conv.keys()) | set(pred_pairs_by_conv.keys())

        # 转换为列表
        all_true_pairs = [true_pairs_by_conv.get(conv_id, []) for conv_id in all_conv_ids]
        all_pred_pairs = [pred_pairs_by_conv.get(conv_id, []) for conv_id in all_conv_ids]

        # 计算对级别指标
        if all_conv_ids:
            pair_metrics = Step2Evaluator.evaluate_pairs(all_true_pairs, all_pred_pairs)
            f1 = pair_metrics['f1']

            if f1 > best_f1:
                best_f1 = f1
                best_threshold = threshold

    logger.info(f"最优阈值: {best_threshold:.3f}, 对应F1: {best_f1:.4f}")
    return best_threshold


def train_epoch(model, dataloader, criterion, optimizer, scheduler, device, logger, args):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    progress_bar = tqdm(dataloader, desc="Training")
    
    for batch_idx, batch in enumerate(progress_bar):
        # 移动数据到设备
        emo_input_ids = batch['emo_input_ids'].to(device)
        emo_attention_mask = batch['emo_attention_mask'].to(device)
        cause_input_ids = batch['cause_input_ids'].to(device)
        cause_attention_mask = batch['cause_attention_mask'].to(device)
        emo_audio = batch['emo_audio'].to(device)
        emo_visual = batch['emo_visual'].to(device)
        cause_audio = batch['cause_audio'].to(device)
        cause_visual = batch['cause_visual'].to(device)
        distance = batch['distance'].to(device)
        emotion_category = batch['emotion_category'].to(device) if args.use_emotion_category else None
        labels = batch['label'].to(device)
        
        # 前向传播
        outputs = model(
            emo_input_ids, emo_attention_mask,
            cause_input_ids, cause_attention_mask,
            emo_audio, emo_visual, cause_audio, cause_visual,
            distance, emotion_category
        )
        
        # 计算损失
        loss = criterion(outputs['logits'], labels)
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        
        # 梯度裁剪
        if args.gradient_clip > 0:
            nn.utils.clip_grad_norm_(model.parameters(), args.gradient_clip)
        
        optimizer.step()
        if scheduler:
            scheduler.step()
        
        # 统计
        total_loss += loss.item()
        
        # 预测结果
        preds = torch.argmax(outputs['logits'], dim=-1)
        all_preds.extend(preds.cpu().tolist())
        all_labels.extend(labels.cpu().tolist())
        
        # 更新进度条
        if batch_idx % args.log_interval == 0:
            current_lr = scheduler.get_last_lr()[0] if scheduler else args.learning_rate
            progress_bar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'LR': f'{current_lr:.6f}'
            })
    
    # 计算平均损失和指标
    avg_loss = total_loss / len(dataloader)
    
    # 计算分类指标
    p, r, f1 = MetricsCalculator.calculate_prf(
        torch.tensor(all_preds), torch.tensor(all_labels)
    )
    
    logger.info(f"Train - Loss: {avg_loss:.4f}, P/R/F1: {p:.4f}/{r:.4f}/{f1:.4f}")
    
    return {
        'loss': avg_loss,
        'precision': p,
        'recall': r,
        'f1': f1
    }


def evaluate(model, dataloader, criterion, device, logger, args, threshold=None):
    """评估模型"""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    all_conversation_ids = []
    all_emo_ids = []
    all_cause_ids = []
    
    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Evaluating"):
            # 移动数据到设备
            emo_input_ids = batch['emo_input_ids'].to(device)
            emo_attention_mask = batch['emo_attention_mask'].to(device)
            cause_input_ids = batch['cause_input_ids'].to(device)
            cause_attention_mask = batch['cause_attention_mask'].to(device)
            emo_audio = batch['emo_audio'].to(device)
            emo_visual = batch['emo_visual'].to(device)
            cause_audio = batch['cause_audio'].to(device)
            cause_visual = batch['cause_visual'].to(device)
            distance = batch['distance'].to(device)
            emotion_category = batch['emotion_category'].to(device) if args.use_emotion_category else None
            labels = batch['label'].to(device)
            
            # 前向传播
            outputs = model(
                emo_input_ids, emo_attention_mask,
                cause_input_ids, cause_attention_mask,
                emo_audio, emo_visual, cause_audio, cause_visual,
                distance, emotion_category
            )
            
            # 计算损失
            loss = criterion(outputs['logits'], labels)
            total_loss += loss.item()
            
            # 收集预测结果
            if threshold is not None:
                # 使用自定义阈值
                probs = torch.softmax(outputs['logits'], dim=-1)[:, 1]  # 正类概率
                preds = (probs >= threshold).long()
            else:
                # 使用默认argmax
                preds = torch.argmax(outputs['logits'], dim=-1)

            all_preds.extend(preds.cpu().tolist())
            all_labels.extend(labels.cpu().tolist())
            all_conversation_ids.extend(batch['conversation_id'])
            all_emo_ids.extend(batch['emo_id'].cpu().tolist())
            all_cause_ids.extend(batch['cause_id'].cpu().tolist())
    
    # 计算平均损失和分类指标
    avg_loss = total_loss / len(dataloader)
    
    p, r, f1 = MetricsCalculator.calculate_prf(
        torch.tensor(all_preds), torch.tensor(all_labels)
    )
    
    # 计算对级别的指标
    # 构建真实对和预测对
    true_pairs_by_conv = defaultdict(list)
    pred_pairs_by_conv = defaultdict(list)

    for i, (conv_id, emo_id, cause_id, pred, label) in enumerate(
        zip(all_conversation_ids, all_emo_ids, all_cause_ids, all_preds, all_labels)
    ):
        if label == 1:  # 真实的正对
            true_pairs_by_conv[conv_id].append((conv_id, emo_id, cause_id))
        if pred == 1:  # 预测的正对
            pred_pairs_by_conv[conv_id].append((conv_id, emo_id, cause_id))

    # 修复：获取所有出现过的对话ID（包括只有预测对、没有真实对的对话）
    all_conv_ids = set(true_pairs_by_conv.keys()) | set(pred_pairs_by_conv.keys())

    # 转换为列表，确保对所有对话进行评价
    all_true_pairs = [true_pairs_by_conv.get(conv_id, []) for conv_id in all_conv_ids]
    all_pred_pairs = [pred_pairs_by_conv.get(conv_id, []) for conv_id in all_conv_ids]

    # 计算对级别指标
    if all_conv_ids:  # 只要有任何对话就进行评价
        pair_metrics = Step2Evaluator.evaluate_pairs(all_true_pairs, all_pred_pairs)
    else:
        pair_metrics = {'precision': 0, 'recall': 0, 'f1': 0}
    
    logger.info(f"Eval - Loss: {avg_loss:.4f}")
    logger.info(f"  分类 P/R/F1: {p:.4f}/{r:.4f}/{f1:.4f}")
    logger.info(f"  对提取 P/R/F1: {pair_metrics['precision']:.4f}/"
                f"{pair_metrics['recall']:.4f}/{pair_metrics['f1']:.4f}")
    
    return {
        'loss': avg_loss,
        'classification_precision': p,
        'classification_recall': r,
        'classification_f1': f1,
        'pair_precision': pair_metrics['precision'],
        'pair_recall': pair_metrics['recall'],
        'pair_f1': pair_metrics['f1']
    }


def main():
    # 解析参数
    config = Config()
    args = config.parse_args()
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    
    # 设置日志
    logger = setup_logging(args.log_dir, args.dataset, args.model_type)
    
    print_time()
    logger.info("开始Step2训练...")
    logger.info(f"数据集: {args.dataset}")
    logger.info(f"模型类型: {args.model_type}")
    logger.info(f"设备: {device}")
    
    # 创建数据加载器
    logger.info("加载数据...")
    train_loader, test_loader, dev_loader, vocab_size = create_data_loaders(
        dataset_name=args.dataset,
        batch_size=args.batch_size,
        stage='step2',
        use_predicted_labels=args.use_predicted_labels,
        pred_future_cause=True,  # 默认允许未来原因，与原论文一致
        step1_pred_dir=args.step1_pred_dir,
        use_emocate=args.use_emocate,
        use_emotion_category=args.use_emotion_category
    )
    
    # 更新配置中的词汇表大小
    args.vocab_size = vocab_size
    logger.info(f"实际词汇表大小: {vocab_size}")
    
    logger.info(f"训练集: {len(train_loader)} 批次")
    logger.info(f"测试集: {len(test_loader)} 批次")
    if dev_loader:
        logger.info(f"验证集: {len(dev_loader)} 批次")
    
    # 创建模型
    logger.info("创建模型...")
    model_config = get_model_config(args, 'step2')
    model = MECPE_Step2_Model(model_config).to(device)
    
    # 打印模型信息
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.info(f"模型参数总数: {total_params:,}")
    logger.info(f"可训练参数: {trainable_params:,}")
    
    # 创建优化器
    if args.model_type == 'bert':
        # BERT使用AdamW
        optimizer = optim.AdamW(
            model.parameters(),
            lr=args.learning_rate,
            weight_decay=args.weight_decay,
            eps=1e-8
        )
        
        # 学习率调度器
        total_steps = len(train_loader) * args.epochs
        scheduler = get_linear_schedule_with_warmup(
            optimizer,
            num_warmup_steps=args.warmup_steps,
            num_training_steps=total_steps
        )
    else:
        # BiLSTM使用Adam
        optimizer = optim.Adam(
            model.parameters(),
            lr=args.learning_rate,
            weight_decay=args.weight_decay
        )
        scheduler = None
    
    # 创建损失函数
    logger.info("计算类别权重...")
    weight_ratio_cap = getattr(args, 'weight_ratio_cap', 3.0)  # 默认限制权重比例为3.0
    class_weights = Step2Loss.compute_class_weights(train_loader, device, weight_ratio_cap)
    criterion = Step2Loss(class_weight=class_weights)
    logger.info(f"使用CrossEntropyLoss，类别权重: {class_weights}")
    
    # 早停和模型保存
    early_stopping = EarlyStopping(patience=args.patience)
    model_saver = ModelSaver(args.save_dir)
    
    # 加载检查点（如果有）
    start_epoch = 0
    best_f1 = 0
    if args.load_checkpoint:
        start_epoch, metrics = model_saver.load_checkpoint(model, optimizer, args.load_checkpoint)
        if metrics:
            best_f1 = metrics.get('best_f1', 0)
            logger.info(f"从检查点恢复训练，起始轮数: {start_epoch}, 最佳F1: {best_f1}")
    
    # 训练循环
    logger.info("开始训练...")
    for epoch in range(start_epoch, args.epochs):
        logger.info(f"\n=== Epoch {epoch + 1}/{args.epochs} ===")
        
        # 训练
        train_metrics = train_epoch(model, train_loader, criterion, optimizer, scheduler, device, logger, args)
        
        # 评估
        if (epoch + 1) % args.eval_interval == 0:
            if dev_loader:
                eval_metrics = evaluate(model, dev_loader, criterion, device, logger, args)
                eval_f1 = eval_metrics['pair_f1']
            else:
                eval_metrics = evaluate(model, test_loader, criterion, device, logger, args)
                eval_f1 = eval_metrics['pair_f1']
            
            # 保存最佳模型
            is_best = eval_f1 > best_f1
            if is_best:
                best_f1 = eval_f1
                logger.info(f"新的最佳对提取F1: {best_f1:.4f}")
            
            # 保存检查点
            if (epoch + 1) % args.save_interval == 0 or is_best:
                metrics = {
                    'epoch': epoch + 1,
                    'train_metrics': train_metrics,
                    'eval_metrics': eval_metrics,
                    'best_f1': best_f1
                }
                model_saver.save_checkpoint(model, optimizer, epoch + 1, metrics, is_best)
            
            # 早停检查
            if early_stopping(eval_metrics['loss'], model):
                logger.info("早停触发，停止训练")
                break
    
    # 最终测试
    logger.info("\n=== 最终测试 ===")
    model_saver.load_checkpoint(model, filename='best_model.pt')

    # 阈值校准（如果有验证集）
    optimal_threshold = None
    if dev_loader:
        logger.info("\n=== 阈值校准 ===")
        optimal_threshold = find_optimal_threshold(model, dev_loader, device, logger)
        logger.info(f"使用校准后的阈值: {optimal_threshold:.3f}")

    # 使用默认阈值的测试结果
    logger.info("\n--- 默认阈值 (0.5) 测试结果 ---")
    test_metrics_default = evaluate(model, test_loader, criterion, device, logger, args)
    logger.info(f"  分类指标 - P: {test_metrics_default['classification_precision']:.4f}, "
                f"R: {test_metrics_default['classification_recall']:.4f}, "
                f"F1: {test_metrics_default['classification_f1']:.4f}")
    logger.info(f"  对提取指标 - P: {test_metrics_default['pair_precision']:.4f}, "
                f"R: {test_metrics_default['pair_recall']:.4f}, "
                f"F1: {test_metrics_default['pair_f1']:.4f}")

    # 使用校准阈值的测试结果（如果有）
    if optimal_threshold is not None:
        logger.info(f"\n--- 校准阈值 ({optimal_threshold:.3f}) 测试结果 ---")
        test_metrics_calibrated = evaluate(model, test_loader, criterion, device, logger, args, threshold=optimal_threshold)
        logger.info(f"  分类指标 - P: {test_metrics_calibrated['classification_precision']:.4f}, "
                    f"R: {test_metrics_calibrated['classification_recall']:.4f}, "
                    f"F1: {test_metrics_calibrated['classification_f1']:.4f}")
        logger.info(f"  对提取指标 - P: {test_metrics_calibrated['pair_precision']:.4f}, "
                    f"R: {test_metrics_calibrated['pair_recall']:.4f}, "
                    f"F1: {test_metrics_calibrated['pair_f1']:.4f}")

        # 对比改进
        f1_improvement = test_metrics_calibrated['pair_f1'] - test_metrics_default['pair_f1']
        p_improvement = test_metrics_calibrated['pair_precision'] - test_metrics_default['pair_precision']
        r_change = test_metrics_calibrated['pair_recall'] - test_metrics_default['pair_recall']
        logger.info(f"\n--- 阈值校准效果 ---")
        logger.info(f"  精确率变化: {p_improvement:+.4f}")
        logger.info(f"  召回率变化: {r_change:+.4f}")
        logger.info(f"  F1变化: {f1_improvement:+.4f}")

    print_time()
    logger.info("Step2训练完成！")


if __name__ == "__main__":
    main()
