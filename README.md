# MECPE PyTorch Implementation

基于PyTorch的多模态情感-原因对提取（Multimodal Emotion-Cause Pair Extraction）实现

## 📁 项目结构

```
MECPE/
├── data/                    # 数据目录
│   ├── iemocap/            # IEMOCAP数据集
│   │   ├── train.h5
│   │   └── test.h5
│   └── meld/               # MELD数据集
│       ├── train.h5
│       ├── dev.h5
│       └── test.h5
├── models/                 # 模型定义
│   ├── __init__.py
│   ├── components.py       # 基础组件
│   ├── step1_model.py      # Step1模型
│   └── step2_model.py      # Step2模型
├── checkpoints/            # 模型检查点
├── logs/                   # 训练日志
├── config.py              # 配置文件
├── data_loader.py         # 数据加载器
├── utils.py               # 工具函数
├── train_step1.py         # Step1训练脚本
├── train_step2.py         # Step2训练脚本
├── run.py                 # 快速运行脚本
├── requirements.txt       # 依赖包
└── README.md              # 说明文档
```

## 🚀 快速开始

### 1. 环境安装

```bash
# 创建虚拟环境（推荐）
conda create -n mecpe python=3.8
conda activate mecpe

# 安装依赖
pip install -r requirements.txt

# 或使用conda安装PyTorch
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia
pip install transformers h5py scikit-learn tqdm
```


### 3. 快速训练（推荐）

使用一键运行脚本，自动完成两阶段训练流程：

```bash
# 运行完整两阶段流程（Step1使用BERT，Step2使用BiLSTM）
python run.py --dataset iemocap --model_type bert

# 使用默认设置（IEMOCAP + Step1使用BiLSTM + Step2使用BiLSTM + 二分类情感检测）
python run.py

# 使用细粒度情感分类（多类）
python run.py --dataset iemocap --model_type bert --use_emocate

# 使用BERT_doc编码方式（更符合原论文）
python run.py --dataset iemocap --model_type bert --bert_encoding_type bert_doc

# 只训练Step1
python run.py --step step1 --dataset iemocap --model_type bert

# 只训练Step2（需要先有Step1模型）
python run.py --step step2 --dataset iemocap --model_type bert

# 使用BiLSTM模型
python run.py --dataset iemocap --model_type bilstm

# 跳过Step1预测生成（如果已存在）
python run.py --dataset iemocap --model_type bert --skip_step1_prediction
```

**完整流程说明**：
1. **Step1训练**: 训练情感和原因检测模型
   - 默认：二分类情感检测（neutral vs non-neutral）
   - `--use_emocate`：细粒度情感分类（IEMOCAP: 6类，MELD: 7类）
2. **生成预测**: 自动使用Step1模型生成train/dev/test的预测结果
3. **Step2训练**: 使用Step1预测结果筛选候选对，训练对提取模型

**情感分类模式**：
- **二分类模式**（默认）：检测话语是否包含情感（neutral vs non-neutral）
- **多分类模式**（`--use_emocate`）：检测具体情感类别，更符合原论文的emocate设置

**BERT编码方式**（仅当model_type=bert时有效）：
- **bert_sen**：独立编码每个话语，使用pooled_output
- **bert_doc**（默认）：拼接整个对话为长序列，按索引提取各话语CLS token，更符合原论文BERT_doc路径

**Step2类别不平衡处理**：
- 使用CrossEntropyLoss with class_weight处理正负样本不平衡
- 自动计算训练集类别权重，给少数类（正样本）更高权重
- 限制正负类权重比例（`--weight_ratio_cap 4.0`），避免过度偏向正类
- 保持[batch_size, 2]的logits输出，概念清晰的二分类实现

**Step2阈值校准**：
- 训练完成后在验证集上搜索最优阈值（范围0.3-0.7）
- 基于正类概率而非argmax进行预测，提高精确率
- 同时报告默认阈值和校准阈值的结果，便于对比



### 4. 详细训练

#### Step1: 情感和原因检测

```bash
# BiLSTM模型
python train_step1.py \
    --dataset iemocap \
    --model_type bilstm \
    --batch_size 32 \
    --learning_rate 0.005 \
    --epochs 30 \
    --use_audio \
    --use_visual

# BERT模型
python train_step1.py \
    --dataset iemocap \
    --model_type bert \
    --batch_size 8 \
    --learning_rate 1e-5 \
    --epochs 15 \
    --use_audio \
    --use_visual
```

#### Step2: 情感-原因对提取

**方法1: 使用Step1预测结果（推荐，符合原论文流程）**

```bash
# 首先从Step1模型生成预测结果
python generate_step1_predictions.py \
    --step1_model_path ./checkpoints/step1/best_model.pt \
    --dataset iemocap \
    --output_dir ./step1_predictions

# 然后使用预测结果训练Step2
python train_step2.py \
    --dataset iemocap \
    --model_type bilstm \
    --batch_size 32 \
    --learning_rate 0.005 \
    --epochs 12 \
    --use_audio \
    --use_visual \
    --use_predicted_labels \
    --step1_pred_dir ./step1_predictions
```

**方法2: 直接使用所有话语对（简化版本）**

```bash
# BiLSTM模型
python train_step2.py \
    --dataset iemocap \
    --model_type bilstm \
    --batch_size 32 \
    --learning_rate 0.005 \
    --epochs 12 \
    --use_audio \
    --use_visual \
    --use_emotion_category

# BERT模型
python train_step2.py \
    --dataset iemocap \
    --model_type bert \
    --batch_size 16 \
    --learning_rate 1e-5 \
    --epochs 12 \
    --use_audio \
    --use_visual
```

## 🏗️ 模型架构

### Step1: 情感和原因检测
- **输入**: 对话序列（文本 + 音频 + 视觉特征）
- **输出**: 每个话语的情感标签和原因标签
- **架构**: 
  - 文本编码器（BiLSTM或BERT）
  - 多模态特征融合
  - 对话级序列编码
  - 情感/原因分类器

### Step2: 情感-原因对提取
- **输入**: 情感话语和原因话语对（基于Step1预测结果筛选候选）
- **输出**: 该对是否构成情感-原因对
- **架构**:
  - 话语编码器（BiLSTM，符合原论文设计）
  - 多模态特征融合
  - 距离编码
  - 二分类器

### 两阶段流程
1. **Step1**: 训练情感和原因检测模型，生成每个话语的预测（支持BiLSTM/BERT）
2. **Step2**: 使用Step1的预测结果筛选候选对，训练对提取模型（固定使用BiLSTM）
3. 这样确保Step2的训练包含Step1的噪声，更符合实际应用场景

> **注意**: 根据原论文设计，Step2固定使用BiLSTM模型。`--model_type`参数仅影响Step1的模型选择。

## 📊 数据集支持

### IEMOCAP
- **情感类别**: 6类（不含neutral）
- **音频特征**: 100维
- **视觉特征**: 100维
- **训练集**: 120个对话，5,810个话语
- **测试集**: 31个对话，1,623个话语

### MELD
- **情感类别**: 6类（不含neutral）
- **音频特征**: 6373维
- **视觉特征**: 4096维
- **训练集**: 984个对话，9,764个话语
- **验证集**: 110个对话，1,069个话语
- **测试集**: 257个对话，2,519个话语

## ⚙️ 主要参数

### 模型参数
- `--model_type`: Step1模型类型（bilstm/bert，Step2固定使用bilstm）
- `--hidden_dim`: 隐藏层维度（默认200）
- `--use_audio`: 是否使用音频特征
- `--use_visual`: 是否使用视觉特征
- `--share_encoder`: 情感和原因是否共享编码器

### 训练参数
- `--batch_size`: 批次大小
- `--learning_rate`: 学习率
- `--epochs`: 训练轮数
- `--dropout`: Dropout率
- `--weight_decay`: 权重衰减

## 📈 评估指标

### Step1
- **情感检测**: Precision, Recall, F1-score
- **原因检测**: Precision, Recall, F1-score

### Step2
- **分类指标**: Precision, Recall, F1-score
- **对提取指标**: 基于真实情感-原因对的P/R/F1