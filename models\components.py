# -*- coding: utf-8 -*-

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np
from transformers import AutoModel, AutoConfig
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from model_path_utils import ensure_model_path_exists


class BiLSTMEncoder(nn.Module):
    """双向LSTM编码器"""
    
    def __init__(self, input_dim, hidden_dim, dropout=0.1, num_layers=1):
        super(BiLSTMEncoder, self).__init__()
        self.hidden_dim = hidden_dim
        # 只有在多层时才使用dropout
        lstm_dropout = dropout if num_layers > 1 else 0
        self.lstm = nn.LSTM(input_dim, hidden_dim, num_layers=num_layers, 
                           batch_first=True, bidirectional=True, dropout=lstm_dropout)
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x, lengths=None):
        """
        Args:
            x: (batch_size, seq_len, input_dim)
            lengths: (batch_size,) 实际序列长度
        Returns:
            output: (batch_size, seq_len, hidden_dim * 2)
        """
        if lengths is not None:
            # 按长度打包序列以提高效率
            packed = nn.utils.rnn.pack_padded_sequence(
                x, lengths.cpu(), batch_first=True, enforce_sorted=False
            )
            output, (h_n, c_n) = self.lstm(packed)
            output, _ = nn.utils.rnn.pad_packed_sequence(output, batch_first=True)
        else:
            output, (h_n, c_n) = self.lstm(x)
        
        return self.dropout(output)


class AttentionLayer(nn.Module):
    """注意力层"""
    
    def __init__(self, hidden_dim):
        super(AttentionLayer, self).__init__()
        self.hidden_dim = hidden_dim
        self.w1 = nn.Linear(hidden_dim, hidden_dim)
        self.w2 = nn.Linear(hidden_dim, 1)
        self.tanh = nn.Tanh()
        self.softmax = nn.Softmax(dim=-1)
    
    def forward(self, x, mask=None):
        """
        Args:
            x: (batch_size, seq_len, hidden_dim)
            mask: (batch_size, seq_len) 1表示有效位置，0表示padding
        Returns:
            attended: (batch_size, hidden_dim)
            attention_weights: (batch_size, seq_len)
        """
        # 计算注意力权重
        u = self.tanh(self.w1(x))  # (batch_size, seq_len, hidden_dim)
        scores = self.w2(u).squeeze(-1)  # (batch_size, seq_len)
        
        # 应用mask
        if mask is not None:
            # 确保mask和scores的维度匹配
            seq_len = scores.size(-1)
            if mask.size(-1) != seq_len:
                # 如果mask长度不匹配，截断或填充
                if mask.size(-1) > seq_len:
                    mask = mask[:, :seq_len]
                else:
                    # 用0填充（表示padding位置）
                    batch_size = mask.size(0)
                    padded_mask = torch.zeros(batch_size, seq_len, device=mask.device, dtype=mask.dtype)
                    padded_mask[:, :mask.size(-1)] = mask
                    mask = padded_mask
            
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attention_weights = self.softmax(scores)  # (batch_size, seq_len)
        
        # 加权求和
        attended = torch.sum(x * attention_weights.unsqueeze(-1), dim=1)  # (batch_size, hidden_dim)
        
        return attended, attention_weights


class MultiHeadAttention(nn.Module):
    """多头注意力机制"""
    
    def __init__(self, d_model, n_heads, dropout=0.1):
        super(MultiHeadAttention, self).__init__()
        assert d_model % n_heads == 0
        
        self.d_model = d_model
        self.n_heads = n_heads
        self.d_k = d_model // n_heads
        
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.scale = math.sqrt(self.d_k)
    
    def forward(self, query, key, value, mask=None):
        batch_size = query.size(0)
        seq_len = query.size(1)
        
        # 线性变换并重塑为多头
        Q = self.w_q(query).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        K = self.w_k(key).view(batch_size, -1, self.n_heads, self.d_k).transpose(1, 2)
        V = self.w_v(value).view(batch_size, -1, self.n_heads, self.d_k).transpose(1, 2)
        
        # 计算注意力
        scores = torch.matmul(Q, K.transpose(-2, -1)) / self.scale
        
        if mask is not None:
            mask = mask.unsqueeze(1).unsqueeze(1)  # (batch_size, 1, 1, seq_len)
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # 应用注意力权重
        attended = torch.matmul(attention_weights, V)
        
        # 重新整合多头
        attended = attended.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.d_model
        )
        
        return self.w_o(attended)


class PositionalEncoding(nn.Module):
    """位置编码"""
    
    def __init__(self, d_model, max_len=512):
        super(PositionalEncoding, self).__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len).unsqueeze(1).float()
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() *
                           -(math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        self.register_buffer('pe', pe.unsqueeze(0))
    
    def forward(self, x):
        return x + self.pe[:, :x.size(1)]


class TransformerBlock(nn.Module):
    """Transformer块"""
    
    def __init__(self, d_model, n_heads, d_ff, dropout=0.1):
        super(TransformerBlock, self).__init__()
        
        self.attention = MultiHeadAttention(d_model, n_heads, dropout)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, d_ff),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_ff, d_model),
            nn.Dropout(dropout)
        )
    
    def forward(self, x, mask=None):
        # 自注意力
        attended = self.attention(x, x, x, mask)
        x = self.norm1(x + attended)
        
        # 前馈网络
        ff_out = self.feed_forward(x)
        x = self.norm2(x + ff_out)
        
        return x


class BERTEncoder(nn.Module):
    """BERT编码器封装"""
    
    def __init__(self, model_name='../roberta', hidden_dim=768, 
                 dropout=0.1, freeze_bert=False):
        super(BERTEncoder, self).__init__()
        
        model_name = ensure_model_path_exists(model_name)
        self.bert = AutoModel.from_pretrained(model_name)
        self.hidden_dim = hidden_dim
        
        if freeze_bert:
            for param in self.bert.parameters():
                param.requires_grad = False
        
        # 如果需要调整维度
        if self.bert.config.hidden_size != hidden_dim:
            self.projection = nn.Linear(self.bert.config.hidden_size, hidden_dim)
        else:
            self.projection = None
        
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, input_ids, attention_mask=None, output_type='sequence'):
        """
        Args:
            input_ids: (batch_size, seq_len) 或 (batch_size, doc_len, seq_len)
            attention_mask: 对应的attention mask
            output_type: 'sequence' 或 'pooled'
        """
        original_shape = input_ids.shape
        
        # 如果是3D输入，重塑为2D
        if len(original_shape) == 3:
            batch_size, doc_len, seq_len = original_shape
            input_ids = input_ids.view(-1, seq_len)
            if attention_mask is not None:
                attention_mask = attention_mask.view(-1, seq_len)
        
        # BERT编码
        outputs = self.bert(input_ids=input_ids, attention_mask=attention_mask)
        
        if output_type == 'pooled':
            encoded = outputs.pooler_output  # (batch_size * doc_len, hidden_size)
        else:
            encoded = outputs.last_hidden_state  # (batch_size * doc_len, seq_len, hidden_size)
        
        # 投影到目标维度
        if self.projection is not None:
            encoded = self.projection(encoded)
        
        encoded = self.dropout(encoded)
        
        # 恢复原始形状
        if len(original_shape) == 3:
            if output_type == 'pooled':
                encoded = encoded.view(batch_size, doc_len, -1)
            else:
                encoded = encoded.view(batch_size, doc_len, seq_len, -1)
        
        return encoded


class BERTDocEncoder(nn.Module):
    """BERT_doc编码器：拼接整个对话为长序列，按索引提取各话语CLS token"""

    def __init__(self, model_name='../roberta', hidden_dim=768,
                 dropout=0.1, freeze_bert=False, max_doc_len=35, max_sen_len=35):
        super(BERTDocEncoder, self).__init__()

        model_name = ensure_model_path_exists(model_name)
        self.bert = AutoModel.from_pretrained(model_name)
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.hidden_dim = hidden_dim
        self.max_doc_len = max_doc_len
        self.max_sen_len = max_sen_len

        if freeze_bert:
            for param in self.bert.parameters():
                param.requires_grad = False

        # 如果需要调整维度
        if self.bert.config.hidden_size != hidden_dim:
            self.projection = nn.Linear(self.bert.config.hidden_size, hidden_dim)
        else:
            self.projection = None

        self.dropout = nn.Dropout(dropout)

    def forward(self, texts, doc_lengths):
        """
        Args:
            texts: List[List[str]] - batch中每个对话的话语文本列表
            doc_lengths: (batch_size,) - 每个对话的实际长度
        Returns:
            encoded: (batch_size, max_doc_len, hidden_dim) - 每个话语的CLS表示
        """
        batch_size = len(texts)
        device = next(self.bert.parameters()).device

        # 存储结果
        batch_utterance_features = []

        for batch_idx in range(batch_size):
            doc_texts = texts[batch_idx]
            actual_doc_len = doc_lengths[batch_idx].item()

            # 构建完整对话序列，每个话语前插入[SEP]作为话语标记
            # 格式：[CLS] utt1 [SEP] utt2 [SEP] ... uttN [SEP]
            full_tokens = []
            utterance_start_positions = []  # 记录每个话语开始位置

            # 添加全局[CLS]
            full_tokens.append(self.tokenizer.cls_token)

            for utt_idx in range(actual_doc_len):
                if utt_idx < len(doc_texts):
                    utt_text = doc_texts[utt_idx].strip()
                else:
                    utt_text = ""  # padding话语

                if utt_idx == 0:
                    # 第一个话语：直接记录CLS位置
                    utterance_start_positions.append(0)
                else:
                    # 后续话语：先添加SEP作为话语分隔符，记录SEP位置作为该话语的"CLS"
                    full_tokens.append(self.tokenizer.sep_token)
                    utterance_start_positions.append(len(full_tokens) - 1)

                # 添加话语tokens（不包括特殊token）
                if utt_text:
                    utt_tokens = self.tokenizer.tokenize(utt_text)
                    full_tokens.extend(utt_tokens)

            # 添加最终的SEP
            full_tokens.append(self.tokenizer.sep_token)

            # 转换为ID并处理长度
            token_ids = self.tokenizer.convert_tokens_to_ids(full_tokens)
            max_length = self.max_doc_len * self.max_sen_len

            if len(token_ids) > max_length:
                token_ids = token_ids[:max_length]
                # 调整超出范围的位置
                utterance_start_positions = [pos for pos in utterance_start_positions if pos < max_length]

            # 创建attention mask和padding
            attention_mask = [1] * len(token_ids)
            while len(token_ids) < max_length:
                token_ids.append(self.tokenizer.pad_token_id)
                attention_mask.append(0)

            input_ids = torch.tensor([token_ids], device=device)
            attention_mask_tensor = torch.tensor([attention_mask], device=device)

            # BERT编码
            with torch.set_grad_enabled(self.training):
                outputs = self.bert(input_ids=input_ids, attention_mask=attention_mask_tensor)
                sequence_output = outputs.last_hidden_state  # (1, seq_len, hidden_size)

            # 提取各话语的表示
            utterance_features = []
            for utt_idx in range(self.max_doc_len):
                if utt_idx < actual_doc_len and utt_idx < len(utterance_start_positions):
                    # 提取对应位置的token表示
                    pos = utterance_start_positions[utt_idx]
                    if pos < sequence_output.size(1):
                        utt_feat = sequence_output[0, pos, :]  # (hidden_size,)
                    else:
                        # 位置超出序列长度，使用零向量
                        utt_feat = torch.zeros(sequence_output.size(-1), device=device)
                else:
                    # padding话语，使用零向量
                    utt_feat = torch.zeros(sequence_output.size(-1), device=device)

                utterance_features.append(utt_feat)

            # 堆叠为 (max_doc_len, hidden_size)
            doc_features = torch.stack(utterance_features, dim=0)
            batch_utterance_features.append(doc_features)

        # 堆叠为 (batch_size, max_doc_len, hidden_size)
        encoded = torch.stack(batch_utterance_features, dim=0)

        # 投影到目标维度
        if self.projection is not None:
            encoded = self.projection(encoded)

        encoded = self.dropout(encoded)

        return encoded


class MultimodalFusion(nn.Module):
    """多模态特征融合"""
    
    def __init__(self, text_dim, audio_dim, visual_dim, hidden_dim, 
                 fusion_type='concat', dropout=0.1):
        super(MultimodalFusion, self).__init__()
        
        self.fusion_type = fusion_type
        self.text_dim = text_dim
        self.audio_dim = audio_dim
        self.visual_dim = visual_dim
        self.hidden_dim = hidden_dim
        
        # 模态特异性投影
        self.text_proj = nn.Linear(text_dim, hidden_dim)
        self.audio_proj = nn.Linear(audio_dim, hidden_dim) 
        self.visual_proj = nn.Linear(visual_dim, hidden_dim)
        
        # 融合策略
        if fusion_type == 'concat':
            self.fusion_layer = nn.Linear(hidden_dim * 3, hidden_dim)
        elif fusion_type == 'attention':
            self.attention = nn.MultiheadAttention(hidden_dim, num_heads=8, dropout=dropout)
        elif fusion_type == 'gate':
            self.gate = nn.Linear(hidden_dim * 3, 3)
            
        self.layer_norm = nn.LayerNorm(hidden_dim)
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, text_feat, audio_feat, visual_feat):
        """
        Args:
            text_feat: (batch_size, seq_len, text_dim) 或 (batch_size, text_dim)
            audio_feat: (batch_size, seq_len, audio_dim) 或 (batch_size, audio_dim)
            visual_feat: (batch_size, seq_len, visual_dim) 或 (batch_size, visual_dim)
        """
        # 投影到统一维度
        text_proj = self.text_proj(text_feat)
        audio_proj = self.audio_proj(audio_feat)
        visual_proj = self.visual_proj(visual_feat)
        
        if self.fusion_type == 'concat':
            # 简单拼接
            fused = torch.cat([text_proj, audio_proj, visual_proj], dim=-1)
            fused = self.fusion_layer(fused)
            
        elif self.fusion_type == 'attention':
            # 注意力融合
            modalities = torch.stack([text_proj, audio_proj, visual_proj], dim=-2)  # (..., 3, hidden_dim)
            if len(modalities.shape) == 3:  # (batch_size, 3, hidden_dim)
                modalities = modalities.transpose(0, 1)  # (3, batch_size, hidden_dim)
                fused, _ = self.attention(modalities, modalities, modalities)
                fused = fused.mean(dim=0)  # (batch_size, hidden_dim)
            else:  # (batch_size, seq_len, 3, hidden_dim)
                batch_size, seq_len = modalities.shape[:2]
                modalities = modalities.view(-1, 3, self.hidden_dim).transpose(0, 1)
                fused, _ = self.attention(modalities, modalities, modalities)
                fused = fused.mean(dim=0).view(batch_size, seq_len, self.hidden_dim)
                
        elif self.fusion_type == 'gate':
            # 门控融合
            concat_feat = torch.cat([text_proj, audio_proj, visual_proj], dim=-1)
            gate_weights = F.softmax(self.gate(concat_feat), dim=-1)
            
            modalities = torch.stack([text_proj, audio_proj, visual_proj], dim=-1)
            fused = torch.sum(modalities * gate_weights.unsqueeze(-2), dim=-1)
        
        else:
            # 默认简单平均
            fused = (text_proj + audio_proj + visual_proj) / 3
        
        fused = self.layer_norm(fused)
        fused = self.dropout(fused)
        
        return fused


class FeatureProjection(nn.Module):
    """特征投影层"""
    
    def __init__(self, input_dim, output_dim, activation='relu', dropout=0.1):
        super(FeatureProjection, self).__init__()
        
        self.projection = nn.Linear(input_dim, output_dim)
        
        if activation == 'relu':
            self.activation = nn.ReLU()
        elif activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'tanh':
            self.activation = nn.Tanh()
        else:
            self.activation = None
            
        self.layer_norm = nn.LayerNorm(output_dim)
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x):
        x = self.projection(x)
        if self.activation is not None:
            x = self.activation(x)
        x = self.layer_norm(x)
        x = self.dropout(x)
        return x


class MaskGenerator(nn.Module):
    """掩码生成器"""
    
    @staticmethod
    def create_padding_mask(lengths, max_len):
        """创建padding掩码"""
        batch_size = lengths.size(0)
        mask = torch.arange(max_len, device=lengths.device).expand(batch_size, max_len) < lengths.unsqueeze(1)
        return mask
    
    @staticmethod
    def create_causal_mask(seq_len):
        """创建因果掩码（下三角矩阵）"""
        mask = torch.tril(torch.ones(seq_len, seq_len))
        return mask.bool()


class GraphAttentionLayer(nn.Module):
    """
    Graph Attention Layer (GAT)
    用于对话级图神经网络建模
    """

    def __init__(self, in_features, out_features, n_heads=8, dropout=0.1,
                 alpha=0.2, concat=True, bias=True):
        """
        Args:
            in_features: 输入特征维度
            out_features: 输出特征维度
            n_heads: 注意力头数
            dropout: dropout率
            alpha: LeakyReLU的负斜率
            concat: 是否拼接多头输出
            bias: 是否使用偏置
        """
        super(GraphAttentionLayer, self).__init__()
        self.in_features = in_features
        self.out_features = out_features
        self.n_heads = n_heads
        self.dropout = dropout
        self.alpha = alpha
        self.concat = concat

        # 每个头的输出维度
        self.head_dim = out_features // n_heads if concat else out_features

        # 线性变换矩阵
        self.W = nn.Parameter(torch.empty(size=(n_heads, in_features, self.head_dim)))
        # 注意力参数
        self.a = nn.Parameter(torch.empty(size=(n_heads, 2 * self.head_dim, 1)))

        # 偏置
        if bias and concat:
            self.bias = nn.Parameter(torch.empty(out_features))
        elif bias and not concat:
            self.bias = nn.Parameter(torch.empty(self.head_dim))
        else:
            self.register_parameter('bias', None)

        self.leakyrelu = nn.LeakyReLU(self.alpha)
        self.dropout_layer = nn.Dropout(dropout)

        self.reset_parameters()

    def reset_parameters(self):
        """初始化参数"""
        nn.init.xavier_uniform_(self.W)
        nn.init.xavier_uniform_(self.a)
        if self.bias is not None:
            nn.init.constant_(self.bias, 0)

    def forward(self, h, adj_matrix, mask=None):
        """
        Args:
            h: 节点特征 [batch_size, n_nodes, in_features]
            adj_matrix: 邻接矩阵 [batch_size, n_nodes, n_nodes]
            mask: 节点掩码 [batch_size, n_nodes]

        Returns:
            output: [batch_size, n_nodes, out_features]
            attention_weights: [batch_size, n_heads, n_nodes, n_nodes]
        """
        batch_size, n_nodes, _ = h.size()

        # 线性变换: [batch_size, n_heads, n_nodes, head_dim]
        Wh = torch.matmul(h.unsqueeze(1), self.W)  # [B, H, N, head_dim]

        # 计算注意力分数
        attention_scores = self._compute_attention_scores(Wh)  # [B, H, N, N]

        # 应用邻接矩阵掩码
        attention_scores = attention_scores.masked_fill(
            adj_matrix.unsqueeze(1) == 0, -1e9
        )

        # 应用节点掩码
        if mask is not None:
            # mask: [B, N] -> [B, 1, N, 1] 和 [B, 1, 1, N]
            node_mask = mask.unsqueeze(1).unsqueeze(-1)  # [B, 1, N, 1]
            attention_scores = attention_scores.masked_fill(~node_mask, -1e9)
            attention_scores = attention_scores.masked_fill(~node_mask.transpose(-2, -1), -1e9)

        # Softmax归一化
        attention_weights = F.softmax(attention_scores, dim=-1)
        attention_weights = self.dropout_layer(attention_weights)

        # 聚合邻居特征
        output = torch.matmul(attention_weights, Wh)  # [B, H, N, head_dim]

        if self.concat:
            # 拼接多头输出
            output = output.transpose(1, 2).contiguous().view(
                batch_size, n_nodes, self.n_heads * self.head_dim
            )
        else:
            # 平均多头输出
            output = output.mean(dim=1)

        # 添加偏置
        if self.bias is not None:
            output = output + self.bias

        return output, attention_weights

    def _compute_attention_scores(self, Wh):
        """计算注意力分数"""
        batch_size, n_heads, n_nodes, head_dim = Wh.size()

        # 扩展为所有节点对: [B, H, N, N, 2*head_dim]
        Wh_i = Wh.unsqueeze(3).expand(-1, -1, -1, n_nodes, -1)  # [B, H, N, N, head_dim]
        Wh_j = Wh.unsqueeze(2).expand(-1, -1, n_nodes, -1, -1)  # [B, H, N, N, head_dim]
        Wh_concat = torch.cat([Wh_i, Wh_j], dim=-1)  # [B, H, N, N, 2*head_dim]

        # 计算注意力分数: [B, H, N, N]
        # self.a: [n_heads, 2*head_dim, 1]
        # Wh_concat: [B, H, N, N, 2*head_dim]
        # 需要对每个头分别计算
        e = torch.zeros(batch_size, n_heads, n_nodes, n_nodes, device=Wh.device)
        for h in range(n_heads):
            # [B, N, N, 2*head_dim] @ [2*head_dim, 1] -> [B, N, N, 1] -> [B, N, N]
            e[:, h, :, :] = torch.matmul(Wh_concat[:, h, :, :, :], self.a[h]).squeeze(-1)

        return self.leakyrelu(e)


class ConversationGAT(nn.Module):
    """
    对话图注意力网络
    专门用于对话场景的图神经网络建模
    """

    def __init__(self, hidden_dim, n_heads=8, n_layers=1, dropout=0.1,
                 use_residual=True, use_layer_norm=True):
        """
        Args:
            hidden_dim: 隐藏层维度
            n_heads: 注意力头数
            n_layers: GAT层数
            dropout: dropout率
            use_residual: 是否使用残差连接
            use_layer_norm: 是否使用层归一化
        """
        super(ConversationGAT, self).__init__()
        self.hidden_dim = hidden_dim
        self.n_heads = n_heads
        self.n_layers = n_layers
        self.use_residual = use_residual
        self.use_layer_norm = use_layer_norm

        # GAT层
        self.gat_layers = nn.ModuleList()
        for i in range(n_layers):
            if i == n_layers - 1:
                # 最后一层不拼接多头，直接平均
                self.gat_layers.append(
                    GraphAttentionLayer(
                        hidden_dim, hidden_dim, n_heads, dropout, concat=False
                    )
                )
            else:
                self.gat_layers.append(
                    GraphAttentionLayer(
                        hidden_dim, hidden_dim, n_heads, dropout, concat=True
                    )
                )

        # 层归一化
        if use_layer_norm:
            self.layer_norms = nn.ModuleList([
                nn.LayerNorm(hidden_dim) for _ in range(n_layers)
            ])

        # Dropout
        self.dropout = nn.Dropout(dropout)

    def forward(self, x, adj_matrix, mask=None):
        """
        Args:
            x: 节点特征 [batch_size, n_nodes, hidden_dim]
            adj_matrix: 邻接矩阵 [batch_size, n_nodes, n_nodes]
            mask: 节点掩码 [batch_size, n_nodes]

        Returns:
            output: [batch_size, n_nodes, hidden_dim]
            all_attention_weights: List of attention weights for each layer
        """
        output = x
        all_attention_weights = []

        for i, gat_layer in enumerate(self.gat_layers):
            # GAT前向传播
            gat_output, attention_weights = gat_layer(output, adj_matrix, mask)
            all_attention_weights.append(attention_weights)

            # 残差连接
            if self.use_residual and gat_output.size(-1) == output.size(-1):
                gat_output = gat_output + output

            # 层归一化
            if self.use_layer_norm:
                gat_output = self.layer_norms[i](gat_output)

            # Dropout
            output = self.dropout(gat_output)

        return output, all_attention_weights


def build_conversation_graph(speakers, doc_lengths, max_doc_len, window_size=2,
                           use_speaker_edges=True, distance_decay=True, temperature=2.0):
    """
    构建对话图的邻接矩阵

    Args:
        speakers: List[List[str]] - 每个对话的说话人序列
        doc_lengths: torch.Tensor [batch_size] - 实际对话长度
        max_doc_len: int - 最大对话长度
        window_size: int - 时序窗口大小
        use_speaker_edges: bool - 是否使用说话人边
        distance_decay: bool - 是否使用距离衰减权重
        temperature: float - 距离衰减的温度参数

    Returns:
        adjacency: torch.Tensor [batch_size, max_doc_len, max_doc_len]
    """
    batch_size = len(speakers)
    device = doc_lengths.device
    adjacency = torch.zeros(batch_size, max_doc_len, max_doc_len, device=device)

    for b in range(batch_size):
        doc_len = doc_lengths[b].item()

        # 1. 自环
        for i in range(doc_len):
            adjacency[b, i, i] = 1.0

        # 2. 时序窗口边
        for i in range(doc_len):
            for j in range(max(0, i - window_size), min(doc_len, i + window_size + 1)):
                if i != j:
                    if distance_decay:
                        # 距离衰减权重
                        weight = torch.exp(-torch.abs(torch.tensor(i - j, dtype=torch.float)) / temperature)
                        adjacency[b, i, j] = weight
                    else:
                        adjacency[b, i, j] = 1.0

        # 3. 同说话人边
        if use_speaker_edges and len(speakers[b]) >= doc_len:
            for i in range(doc_len):
                for j in range(doc_len):
                    if i != j and speakers[b][i] == speakers[b][j]:
                        # 同说话人边可以有额外的权重偏置
                        current_weight = adjacency[b, i, j].item()
                        adjacency[b, i, j] = max(current_weight, 0.8)  # 同说话人边至少0.8权重

    return adjacency


if __name__ == "__main__":
    # 测试各个组件
    batch_size, seq_len, hidden_dim = 4, 10, 256
    
    # 测试BiLSTM
    print("测试BiLSTM编码器...")
    x = torch.randn(batch_size, seq_len, 300)
    lengths = torch.tensor([10, 8, 6, 9])
    
    bilstm = BiLSTMEncoder(300, 128)
    output = bilstm(x, lengths)
    print(f"BiLSTM输出形状: {output.shape}")
    
    # 测试注意力
    print("\n测试注意力层...")
    attention = AttentionLayer(256)
    mask = MaskGenerator.create_padding_mask(lengths, seq_len)
    attended, weights = attention(output, mask)
    print(f"注意力输出形状: {attended.shape}")
    print(f"注意力权重形状: {weights.shape}")
    
    # 测试多模态融合
    print("\n测试多模态融合...")
    text_feat = torch.randn(batch_size, 256)
    audio_feat = torch.randn(batch_size, 100)
    visual_feat = torch.randn(batch_size, 100)
    
    fusion = MultimodalFusion(256, 100, 100, 256, fusion_type='attention')
    fused = fusion(text_feat, audio_feat, visual_feat)
    print(f"融合特征形状: {fused.shape}")

    # 测试GAT
    print("\n测试对话GAT...")
    conv_features = torch.randn(batch_size, seq_len, 256)
    speakers = [['A', 'B', 'A', 'B', 'A', 'B', 'A', 'B', 'A', 'B'] for _ in range(batch_size)]
    doc_lengths = torch.tensor([10, 8, 6, 9])

    # 构建图
    adj_matrix = build_conversation_graph(speakers, doc_lengths, seq_len, window_size=2)
    print(f"邻接矩阵形状: {adj_matrix.shape}")

    # GAT前向传播
    conv_gat = ConversationGAT(256, n_heads=4, n_layers=1)
    gat_output, attention_weights = conv_gat(conv_features, adj_matrix, mask)
    print(f"GAT输出形状: {gat_output.shape}")
    print(f"注意力权重形状: {attention_weights[0].shape}")

    print("\n所有组件测试完成！")
