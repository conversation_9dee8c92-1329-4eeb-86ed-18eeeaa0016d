# -*- coding: utf-8 -*-

import os
import sys
import argparse
import subprocess

from model_path_utils import get_roberta_model_path

def run_step1(dataset='meld', model_type='bilstm', use_audio=True, use_visual=True,
              batch_size=8, epochs=30, learning_rate=1e-5, use_emocate=False, bert_encoding_type='bert_sen',
              use_gat=True, gat_n_heads=8, gat_n_layers=1, gat_window_size=2,
              gat_use_speaker_edges=True, gat_distance_decay=True):
    """运行Step1训练"""
    cmd = [
        sys.executable, 'train_step1.py',
        '--dataset', dataset,
        '--model_type', model_type,
        '--batch_size', str(batch_size),
        '--epochs', str(epochs),
        '--learning_rate', str(learning_rate),
        '--save_dir', './checkpoints/step1',
        '--log_dir', './logs/step1'
    ]

    if use_audio:
        cmd.append('--use_audio')
    if use_visual:
        cmd.append('--use_visual')
    if use_emocate:
        cmd.append('--use_emocate')
    if model_type == 'bert' and bert_encoding_type == 'bert_doc':
        cmd.extend(['--bert_encoding_type', bert_encoding_type])

    # 添加GAT参数
    if use_gat:
        cmd.append('--use_gat')
    cmd.extend(['--gat_n_heads', str(gat_n_heads)])
    cmd.extend(['--gat_n_layers', str(gat_n_layers)])
    cmd.extend(['--gat_window_size', str(gat_window_size)])
    if gat_use_speaker_edges:
        cmd.append('--gat_use_speaker_edges')
    if gat_distance_decay:
        cmd.append('--gat_distance_decay')

    print(f"运行命令: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print("❌ Step1训练失败!")
        return False
    print("✓ Step1训练完成")
    return True

def generate_step1_predictions(dataset='meld', model_type='bilstm', use_emocate=False, bert_encoding_type='bert_sen',
                             use_gat=True, gat_n_heads=8, gat_n_layers=1, gat_window_size=2,
                             gat_use_speaker_edges=True, gat_distance_decay=True):
    """生成Step1预测结果"""
    # 确定Step1模型路径
    base_dir = './checkpoints/step1'
    best_path = os.path.join(base_dir, 'best_model.pt')
    step1_model_path = None
    if os.path.exists(best_path):
        step1_model_path = best_path
    else:
        # 回退到最近的checkpoint_epoch_*.pt
        if os.path.exists(base_dir):
            candidates = [f for f in os.listdir(base_dir) if f.startswith('checkpoint_epoch_') and f.endswith('.pt')]
            if candidates:
                # 按 epoch 值排序，取最新
                candidates.sort(key=lambda x: int(x.split('_')[-1].split('.')[0]))
                step1_model_path = os.path.join(base_dir, candidates[-1])
    if not step1_model_path:
        print(f"❌ 找不到Step1模型: {best_path} 或任何 checkpoint_epoch_*.pt")
        return False
    print(f"✓ 使用Step1模型: {step1_model_path}")

    # 设置预测结果输出目录
    pred_dir = f"./step1_predictions/{dataset}_{model_type}"
    os.makedirs(pred_dir, exist_ok=True)

    cmd = [
        sys.executable, 'generate_step1_predictions.py',
        '--step1_model_path', step1_model_path,
        '--dataset', dataset,
        '--model_type', model_type,
        '--output_dir', pred_dir,
        '--batch_size', '16'  # 预测时可以用更大的batch size
    ]

    if use_emocate:
        cmd.append('--use_emocate')
    if model_type == 'bert' and bert_encoding_type == 'bert_doc':
        cmd.extend(['--bert_encoding_type', bert_encoding_type])

    # 添加GAT参数
    if use_gat:
        cmd.append('--use_gat')
    cmd.extend(['--gat_n_heads', str(gat_n_heads)])
    cmd.extend(['--gat_n_layers', str(gat_n_layers)])
    cmd.extend(['--gat_window_size', str(gat_window_size)])
    if gat_use_speaker_edges:
        cmd.append('--gat_use_speaker_edges')
    if gat_distance_decay:
        cmd.append('--gat_distance_decay')

    print(f"运行命令: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print("❌ Step1预测生成失败!")
        return False
    print("✓ Step1预测结果生成完成")
    return pred_dir

def run_step2(dataset='meld', use_audio=True, use_visual=True,
              use_emotion_category=False, batch_size=32, epochs=12, learning_rate=0.005,
              step1_pred_dir=None):
    """运行Step2训练（固定使用bilstm模型，符合原论文设计）"""
    cmd = [
        sys.executable, 'train_step2.py',
        '--dataset', dataset,
        '--model_type', 'bilstm',  # Step2固定使用bilstm
        '--batch_size', str(batch_size),
        '--epochs', str(epochs),
        '--learning_rate', str(learning_rate)
    ]

    if use_audio:
        cmd.append('--use_audio')
    if use_visual:
        cmd.append('--use_visual')
    if use_emotion_category:
        cmd.append('--use_emotion_category')

    # 如果提供了Step1预测目录，则使用预测结果
    if step1_pred_dir and os.path.exists(step1_pred_dir):
        cmd.extend(['--use_predicted_labels', '--step1_pred_dir', step1_pred_dir])
        print(f"✓ 使用Step1预测结果: {step1_pred_dir}")
    else:
        print("⚠️  未使用Step1预测结果，将使用所有话语对作为候选")

    print(f"运行命令: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print("❌ Step2训练失败!")
        return False
    print("✓ Step2训练完成")
    return True

def main():
    parser = argparse.ArgumentParser(description='MECPE PyTorch 完整训练脚本')
    parser.add_argument('--step', type=str, choices=['step1', 'step2', 'both'],
                       default='both', help='运行哪个步骤')
    parser.add_argument('--dataset', type=str, choices=['iemocap', 'meld'],
                       default='meld', help='数据集')
    parser.add_argument('--model_type', type=str, choices=['bilstm', 'bert'],
                       default='bilstm', help='Step1模型类型（Step2固定使用bilstm）')
    parser.add_argument('--use_audio', action='store_true', default=True,
                       help='使用音频特征')
    parser.add_argument('--use_visual', action='store_true', default=True,
                       help='使用视觉特征')
    parser.add_argument('--use_emotion_category', action='store_true', default=True,
                       help='使用情感类别（Step2）')
    parser.add_argument('--use_emocate', action='store_true', default=True,
                       help='使用细粒度情感分类（多类），否则使用二分类（neutral vs non-neutral）')
    parser.add_argument('--bert_encoding_type', type=str, default='bert_doc',
                       choices=['bert_sen', 'bert_doc'],
                       help='BERT编码方式：bert_sen(独立编码) 或 bert_doc(拼接对话)')
    parser.add_argument('--skip_step1_prediction', action='store_true', default=False,
                       help='跳过Step1预测生成（如果已存在）')

    # GAT相关参数
    parser.add_argument('--use_gat', action='store_true', default=True,
                       help='使用GAT替换sentence encoder')
    parser.add_argument('--gat_n_heads', type=int, default=8,
                       help='GAT注意力头数')
    parser.add_argument('--gat_n_layers', type=int, default=1,
                       help='GAT层数')
    parser.add_argument('--gat_window_size', type=int, default=2,
                       help='GAT时序窗口大小')
    parser.add_argument('--gat_use_speaker_edges', action='store_true', default=True,
                       help='GAT是否使用说话人边')
    parser.add_argument('--gat_distance_decay', action='store_true', default=True,
                       help='GAT是否使用距离衰减权重')

    args = parser.parse_args()

    print("=== MECPE PyTorch 完整训练流程 ===")

    # 检查本地模型路径
    if args.model_type == 'bert':
        model_path = get_roberta_model_path()
        print(f"本地模型路径: {model_path}")
        if not os.path.exists(model_path):
            print("❌ 错误: 找不到本地RoBERTa模型!")
            print("请确保roberta文件夹位于MECPE_pytorch的同级目录下")
            print("或运行 python test_model_loading.py 进行诊断")
            return
        else:
            print("✓ 本地模型路径检查通过")

    print(f"数据集: {args.dataset}")
    print(f"模型类型: {args.model_type}")
    print(f"使用音频: {args.use_audio}")
    print(f"使用视觉: {args.use_visual}")
    print(f"使用情感类别: {args.use_emotion_category}")
    print(f"使用细粒度情感分类: {args.use_emocate}")
    print(f"BERT编码方式: {args.bert_encoding_type}")

    step1_pred_dir = None

    # Step1: 情感和原因检测
    if args.step in ['step1', 'both']:
        print("\n=== 第一阶段: 情感和原因检测 ===")
        if args.model_type == 'bert':
            success = run_step1(args.dataset, args.model_type, args.use_audio, args.use_visual,
                               batch_size=8, epochs=20, learning_rate=1e-5, use_emocate=args.use_emocate,
                               bert_encoding_type=args.bert_encoding_type,
                               use_gat=args.use_gat, gat_n_heads=args.gat_n_heads,
                               gat_n_layers=args.gat_n_layers, gat_window_size=args.gat_window_size,
                               gat_use_speaker_edges=args.gat_use_speaker_edges,
                               gat_distance_decay=args.gat_distance_decay)
        else:
            success = run_step1(args.dataset, args.model_type, args.use_audio, args.use_visual,
                               batch_size=128, epochs=30, learning_rate=0.005, use_emocate=args.use_emocate,
                               bert_encoding_type=args.bert_encoding_type,
                               use_gat=args.use_gat, gat_n_heads=args.gat_n_heads,
                               gat_n_layers=args.gat_n_layers, gat_window_size=args.gat_window_size,
                               gat_use_speaker_edges=args.gat_use_speaker_edges,
                               gat_distance_decay=args.gat_distance_decay)

        if not success:
            print("❌ Step1训练失败，终止流程")
            return

    # 生成Step1预测结果（如果需要进行Step2）
    if args.step in ['step2', 'both']:
        print("\n=== 生成Step1预测结果 ===")

        # 检查是否已存在预测结果（基于Step1的模型类型）
        potential_pred_dir = f"./step1_predictions/{args.dataset}_{args.model_type}"
        if args.skip_step1_prediction and os.path.exists(potential_pred_dir):
            print(f"✓ 跳过预测生成，使用已存在的结果: {potential_pred_dir}")
            step1_pred_dir = potential_pred_dir
        else:
            step1_pred_dir = generate_step1_predictions(
                args.dataset, args.model_type, args.use_emocate, args.bert_encoding_type,
                args.use_gat, args.gat_n_heads, args.gat_n_layers, args.gat_window_size,
                args.gat_use_speaker_edges, args.gat_distance_decay
            )
            if not step1_pred_dir:
                print("❌ Step1预测生成失败，Step2将使用所有话语对作为候选")
                step1_pred_dir = None

    # Step2: 情感-原因对提取（固定使用bilstm）
    if args.step in ['step2', 'both']:
        print("\n=== 第二阶段: 情感-原因对提取 ===")
        # Step2固定使用bilstm，使用bilstm的超参数配置
        success = run_step2(args.dataset, args.use_audio, args.use_visual,
                           args.use_emotion_category, batch_size=256, epochs=15, learning_rate=0.005,
                           step1_pred_dir=step1_pred_dir)

        if not success:
            print("❌ Step2训练失败")
            return

    print("\n=== 🎉 完整训练流程完成! ===")
    if step1_pred_dir:
        print(f"Step1预测结果保存在: {step1_pred_dir}")
    print("模型检查点保存在: ./checkpoints/")
    print("训练日志保存在: ./logs/")

if __name__ == "__main__":
    main()
