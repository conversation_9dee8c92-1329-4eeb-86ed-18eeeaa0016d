# -*- coding: utf-8 -*-

import torch
import torch.nn as nn
import torch.nn.functional as F
from .components import (
    BiLSTMEncoder, AttentionLayer, BERTEncoder, BERTDocEncoder,
    MultimodalFusion, FeatureProjection, TransformerBlock,
    MaskGenerator, ConversationGAT, build_conversation_graph
)


class MECPE_Step1_Model(nn.Module):
    """
    MECPE Step1模型：情感和原因检测
    支持BiLSTM和BERT两种文本编码器
    """
    
    def __init__(self, config):
        super(MECPE_Step1_Model, self).__init__()
        
        self.config = config
        self.model_type = config.model_type  # 'bilstm' or 'bert'
        self.bert_encoding_type = getattr(config, 'bert_encoding_type', 'bert_sen')  # 'bert_sen' or 'bert_doc'
        self.use_audio = config.use_audio
        self.use_visual = config.use_visual
        self.share_encoder = config.share_encoder
        self.hidden_dim = config.hidden_dim
        self.max_doc_len = config.max_doc_len
        self.max_sen_len = config.max_sen_len
        self.n_emotions = config.n_emotions if hasattr(config, 'n_emotions') else 2

        # GAT配置参数
        self.use_gat = getattr(config, 'use_gat', True)  # 是否使用GAT替换sentence encoder
        self.gat_n_heads = getattr(config, 'gat_n_heads', 8)  # GAT注意力头数
        self.gat_n_layers = getattr(config, 'gat_n_layers', 1)  # GAT层数
        self.gat_window_size = getattr(config, 'gat_window_size', 2)  # 时序窗口大小
        self.gat_use_speaker_edges = getattr(config, 'gat_use_speaker_edges', True)  # 是否使用说话人边
        self.gat_distance_decay = getattr(config, 'gat_distance_decay', True)  # 是否使用距离衰减
        
        # 文本编码器
        if self.model_type == 'bert':
            if self.bert_encoding_type == 'bert_doc':
                # BERT_doc: 拼接对话后按索引提取CLS
                self.text_encoder_emo = BERTDocEncoder(
                    model_name=config.bert_model_name,
                    hidden_dim=self.hidden_dim,
                    dropout=config.dropout,
                    max_doc_len=self.max_doc_len,
                    max_sen_len=self.max_sen_len
                )
                if not self.share_encoder:
                    self.text_encoder_cause = BERTDocEncoder(
                        model_name=config.bert_model_name,
                        hidden_dim=self.hidden_dim,
                        dropout=config.dropout,
                        max_doc_len=self.max_doc_len,
                        max_sen_len=self.max_sen_len
                    )
            else:
                # BERT_sen: 独立编码每个话语
                self.text_encoder_emo = BERTEncoder(
                    model_name=config.bert_model_name,
                    hidden_dim=self.hidden_dim,
                    dropout=config.dropout
                )
                if not self.share_encoder:
                    self.text_encoder_cause = BERTEncoder(
                        model_name=config.bert_model_name,
                        hidden_dim=self.hidden_dim,
                        dropout=config.dropout
                    )
        else:
            # 词嵌入层
            self.word_embedding = nn.Embedding(
                config.vocab_size, config.embedding_dim, padding_idx=0
            )
            self.embedding_dropout = nn.Dropout(config.dropout)
            
            # BiLSTM编码器
            self.text_encoder_emo = BiLSTMEncoder(
                config.embedding_dim, self.hidden_dim // 2, config.dropout
            )
            if not self.share_encoder:
                self.text_encoder_cause = BiLSTMEncoder(
                    config.embedding_dim, self.hidden_dim // 2, config.dropout
                )
            
            # 话语级注意力
            self.utterance_attention_emo = AttentionLayer(self.hidden_dim)
            if not self.share_encoder:
                self.utterance_attention_cause = AttentionLayer(self.hidden_dim)
        
        # 多模态特征处理
        if self.use_audio:
            self.audio_projection = FeatureProjection(
                config.audio_dim, self.hidden_dim, dropout=config.dropout
            )
        
        if self.use_visual:
            self.visual_projection = FeatureProjection(
                config.visual_dim, self.hidden_dim, dropout=config.dropout
            )

        # 如果会拼接模态特征，则预先定义回投影层，避免在forward中动态创建导致state_dict不一致
        if self.use_audio or self.use_visual:
            feature_dim = self.hidden_dim
            if self.use_audio:
                feature_dim += self.hidden_dim
            if self.use_visual:
                feature_dim += self.hidden_dim
            self.feature_projection_emo = nn.Linear(feature_dim, self.hidden_dim)
            self.feature_projection_cause = nn.Linear(feature_dim, self.hidden_dim)

        # 句子级编码器（对话级别）- 使用GAT替换
        if self.use_gat:
            # 使用GAT进行对话级建模
            self.sentence_encoder_emo = ConversationGAT(
                hidden_dim=self.hidden_dim,
                n_heads=self.gat_n_heads,
                n_layers=self.gat_n_layers,
                dropout=config.dropout,
                use_residual=True,
                use_layer_norm=True
            )
            if not self.share_encoder:
                self.sentence_encoder_cause = ConversationGAT(
                    hidden_dim=self.hidden_dim,
                    n_heads=self.gat_n_heads,
                    n_layers=self.gat_n_layers,
                    dropout=config.dropout,
                    use_residual=True,
                    use_layer_norm=True
                )
        else:
            # 使用原有的编码器
            if self.model_type == 'bert':
                self.sentence_encoder_emo = TransformerBlock(
                    self.hidden_dim, config.n_heads, self.hidden_dim * 4, config.dropout
                )
                if not self.share_encoder:
                    self.sentence_encoder_cause = TransformerBlock(
                        self.hidden_dim, config.n_heads, self.hidden_dim * 4, config.dropout
                    )
            else:
                self.sentence_encoder_emo = BiLSTMEncoder(
                    self.hidden_dim, self.hidden_dim // 2, config.dropout
                )
                if not self.share_encoder:
                    self.sentence_encoder_cause = BiLSTMEncoder(
                        self.hidden_dim, self.hidden_dim // 2, config.dropout
                    )
        
        # 分类头
        classifier_input_dim = self.hidden_dim
        
        self.emotion_classifier = nn.Sequential(
            nn.Dropout(config.dropout),
            nn.Linear(classifier_input_dim, self.n_emotions)
        )
        
        self.cause_classifier = nn.Sequential(
            nn.Dropout(config.dropout),
            nn.Linear(classifier_input_dim, 2)  # 0: 非原因, 1: 原因
        )
        
        # 音频和视觉单独的情感分类器（如果使用）
        if self.use_audio:
            self.audio_emotion_classifier = nn.Sequential(
                nn.Dropout(config.dropout),
                nn.Linear(self.hidden_dim, self.n_emotions)
            )
        
        if self.use_visual:
            self.visual_emotion_classifier = nn.Sequential(
                nn.Dropout(config.dropout),
                nn.Linear(self.hidden_dim, self.n_emotions)
            )
    
    def encode_utterances(self, input_ids, attention_mask, doc_lengths, texts=None):
        """编码话语级特征"""
        batch_size, max_doc_len, max_sen_len = input_ids.shape

        if self.model_type == 'bert':
            if self.bert_encoding_type == 'bert_doc':
                # BERT_doc编码 - 需要原始文本
                if texts is None:
                    raise ValueError("BERT_doc编码需要提供原始文本")
                utterance_features = self.text_encoder_emo(texts, doc_lengths)
            else:
                # BERT_sen编码 - 独立编码每个话语
                utterance_features = self.text_encoder_emo(
                    input_ids, attention_mask, output_type='pooled'
                )  # (batch_size, max_doc_len, hidden_dim)
        else:
            # BiLSTM编码
            # 首先进行词嵌入
            embedded = self.word_embedding(input_ids)  # (batch_size, max_doc_len, max_sen_len, embedding_dim)
            embedded = self.embedding_dropout(embedded)
            
            # 重塑为2D以便LSTM处理
            embedded = embedded.view(-1, max_sen_len, self.config.embedding_dim)
            
            # 计算每个话语的实际长度
            utterance_lengths = attention_mask.sum(dim=-1).view(-1)  # (batch_size * max_doc_len,)
            # 确保长度至少为1（避免pack_padded_sequence错误）
            utterance_lengths = torch.clamp(utterance_lengths, min=1)
            
            # BiLSTM编码
            encoded = self.text_encoder_emo(embedded, utterance_lengths)  # (-1, max_sen_len, hidden_dim)
            
            # 话语级注意力
            utterance_mask = attention_mask.view(-1, max_sen_len)
            utterance_features, _ = self.utterance_attention_emo(encoded, utterance_mask)
            
            # 重塑回对话维度
            utterance_features = utterance_features.view(batch_size, max_doc_len, self.hidden_dim)
        
        return utterance_features
    
    def forward(self, input_ids, attention_mask, audio_features, visual_features, doc_lengths, texts=None, speakers=None):
        """
        Args:
            input_ids: (batch_size, max_doc_len, max_sen_len)
            attention_mask: (batch_size, max_doc_len, max_sen_len)
            audio_features: (batch_size, max_doc_len, audio_dim)
            visual_features: (batch_size, max_doc_len, visual_dim)
            doc_lengths: (batch_size,) 实际对话长度
            texts: List[List[str]] 原始文本（BERT_doc需要）
            speakers: List[List[str]] 说话人序列（GAT构图需要）
        """
        batch_size, max_doc_len = input_ids.shape[:2]
        
        # 编码话语特征
        utterance_features_emo = self.encode_utterances(input_ids, attention_mask, doc_lengths, texts)
        
        if self.share_encoder:
            utterance_features_cause = utterance_features_emo
        else:
            if self.model_type == 'bert':
                if self.bert_encoding_type == 'bert_doc':
                    utterance_features_cause = self.text_encoder_cause(texts, doc_lengths)
                else:
                    utterance_features_cause = self.text_encoder_cause(
                        input_ids, attention_mask, output_type='pooled'
                    )
            else:
                embedded = self.word_embedding(input_ids)
                embedded = self.embedding_dropout(embedded)
                embedded = embedded.view(-1, input_ids.shape[-1], self.config.embedding_dim)
                utterance_lengths = attention_mask.sum(dim=-1).view(-1)
                # 确保长度至少为1（避免pack_padded_sequence错误）
                utterance_lengths = torch.clamp(utterance_lengths, min=1)
                encoded = self.text_encoder_cause(embedded, utterance_lengths)
                utterance_mask = attention_mask.view(-1, input_ids.shape[-1])
                utterance_features_cause, _ = self.utterance_attention_cause(encoded, utterance_mask)
                utterance_features_cause = utterance_features_cause.view(batch_size, max_doc_len, self.hidden_dim)
        
        # 处理多模态特征
        audio_feat = None
        visual_feat = None
        
        if self.use_audio:
            audio_feat = self.audio_projection(audio_features)
            
        if self.use_visual:
            visual_feat = self.visual_projection(visual_features)
        
        # 特征融合
        fused_features_emo = utterance_features_emo.clone()
        fused_features_cause = utterance_features_cause.clone()
        
        if self.use_audio:
            fused_features_emo = torch.cat([fused_features_emo, audio_feat], dim=-1)
            fused_features_cause = torch.cat([fused_features_cause, audio_feat], dim=-1)
        
        if self.use_visual:
            fused_features_emo = torch.cat([fused_features_emo, visual_feat], dim=-1)
            fused_features_cause = torch.cat([fused_features_cause, visual_feat], dim=-1)
        
        # 如果有特征拼接，需要投影回原维度（层已在__init__中定义）
        if self.use_audio or self.use_visual:
            fused_features_emo = self.feature_projection_emo(fused_features_emo)
            fused_features_cause = self.feature_projection_cause(fused_features_cause)
        
        # 对话级编码
        doc_mask = MaskGenerator.create_padding_mask(doc_lengths, max_doc_len).to(input_ids.device)

        if self.use_gat:
            # 使用GAT进行对话级建模
            # 构建对话图
            if speakers is not None:
                adj_matrix = build_conversation_graph(
                    speakers, doc_lengths, max_doc_len,
                    window_size=self.gat_window_size,
                    use_speaker_edges=self.gat_use_speaker_edges,
                    distance_decay=self.gat_distance_decay
                )
            else:
                # 如果没有speakers信息，只使用时序边
                adj_matrix = build_conversation_graph(
                    [['A'] * max_doc_len for _ in range(batch_size)],
                    doc_lengths, max_doc_len,
                    window_size=self.gat_window_size,
                    use_speaker_edges=False,
                    distance_decay=self.gat_distance_decay
                )

            # GAT前向传播
            sentence_features_emo, _ = self.sentence_encoder_emo(fused_features_emo, adj_matrix, doc_mask)
            if self.share_encoder:
                sentence_features_cause = sentence_features_emo
            else:
                sentence_features_cause, _ = self.sentence_encoder_cause(fused_features_cause, adj_matrix, doc_mask)
        else:
            # 使用原有的编码器
            if self.model_type == 'bert':
                sentence_features_emo = self.sentence_encoder_emo(fused_features_emo, doc_mask)
                if self.share_encoder:
                    sentence_features_cause = sentence_features_emo
                else:
                    sentence_features_cause = self.sentence_encoder_cause(fused_features_cause, doc_mask)
            else:
                sentence_features_emo = self.sentence_encoder_emo(fused_features_emo, doc_lengths)
                if self.share_encoder:
                    sentence_features_cause = sentence_features_emo
                else:
                    sentence_features_cause = self.sentence_encoder_cause(fused_features_cause, doc_lengths)
        
        # 分类
        emotion_logits = self.emotion_classifier(sentence_features_emo)
        cause_logits = self.cause_classifier(sentence_features_cause)
        
        # 额外的模态特定分类器
        audio_emotion_logits = None
        visual_emotion_logits = None
        
        # 获取主要输出的实际序列长度
        actual_seq_len = emotion_logits.shape[1]
        
        if self.use_audio and audio_feat is not None:
            audio_emotion_logits = self.audio_emotion_classifier(audio_feat)
            # 确保与主要输出维度一致
            audio_emotion_logits = audio_emotion_logits[:, :actual_seq_len, :]
        
        if self.use_visual and visual_feat is not None:
            visual_emotion_logits = self.visual_emotion_classifier(visual_feat)
            # 确保与主要输出维度一致
            visual_emotion_logits = visual_emotion_logits[:, :actual_seq_len, :]
        
        return {
            'emotion_logits': emotion_logits,  # (batch_size, actual_seq_len, n_emotions)
            'cause_logits': cause_logits,      # (batch_size, actual_seq_len, 2)
            'audio_emotion_logits': audio_emotion_logits,  # (batch_size, actual_seq_len, n_emotions) or None
            'visual_emotion_logits': visual_emotion_logits, # (batch_size, actual_seq_len, n_emotions) or None
            'utterance_features_emo': utterance_features_emo,
            'utterance_features_cause': utterance_features_cause
        }


class Step1Config:
    """Step1模型配置"""
    
    def __init__(self, **kwargs):
        # 模型架构
        self.model_type = kwargs.get('model_type', 'bilstm')  # 'bilstm' or 'bert'
        self.hidden_dim = kwargs.get('hidden_dim', 200)
        self.n_heads = kwargs.get('n_heads', 8)
        self.dropout = kwargs.get('dropout', 0.1)
        self.share_encoder = kwargs.get('share_encoder', True)
        
        # 数据维度
        self.vocab_size = kwargs.get('vocab_size', 30000)
        self.embedding_dim = kwargs.get('embedding_dim', 300)
        self.audio_dim = kwargs.get('audio_dim', 100)
        self.visual_dim = kwargs.get('visual_dim', 100)
        self.max_doc_len = kwargs.get('max_doc_len', 35)
        self.max_sen_len = kwargs.get('max_sen_len', 35)
        
        # 模态选择
        self.use_audio = kwargs.get('use_audio', True)
        self.use_visual = kwargs.get('use_visual', True)
        
        # BERT相关
        self.bert_model_name = kwargs.get('bert_model_name', '../roberta')
        
        # 情感类别数
        self.n_emotions = kwargs.get('n_emotions', 2)  # 2: 中性/非中性, 或具体情感类别数

        # GAT相关参数
        self.use_gat = kwargs.get('use_gat', True)
        self.gat_n_heads = kwargs.get('gat_n_heads', 8)
        self.gat_n_layers = kwargs.get('gat_n_layers', 1)
        self.gat_window_size = kwargs.get('gat_window_size', 2)
        self.gat_use_speaker_edges = kwargs.get('gat_use_speaker_edges', True)
        self.gat_distance_decay = kwargs.get('gat_distance_decay', True)


if __name__ == "__main__":
    # 测试Step1模型
    print("测试MECPE Step1模型...")
    
    # 配置
    config = Step1Config(
        model_type='bilstm',
        hidden_dim=200,
        vocab_size=10000,
        embedding_dim=300,
        audio_dim=100,
        visual_dim=100,
        use_audio=True,
        use_visual=True,
        n_emotions=2
    )
    
    # 创建模型
    model = MECPE_Step1_Model(config)
    
    # 测试数据
    batch_size, max_doc_len, max_sen_len = 4, 10, 20

    input_ids = torch.randint(1, 1000, (batch_size, max_doc_len, max_sen_len))
    attention_mask = torch.ones(batch_size, max_doc_len, max_sen_len)
    audio_features = torch.randn(batch_size, max_doc_len, 100)
    visual_features = torch.randn(batch_size, max_doc_len, 100)
    doc_lengths = torch.tensor([10, 8, 6, 9])
    speakers = [['A', 'B', 'A', 'B', 'A', 'B', 'A', 'B', 'A', 'B'] for _ in range(batch_size)]

    # 前向传播
    with torch.no_grad():
        outputs = model(input_ids, attention_mask, audio_features, visual_features, doc_lengths, speakers=speakers)
    
    print("输出形状:")
    for key, value in outputs.items():
        if value is not None:
            print(f"  {key}: {value.shape}")
    
    print("\nStep1模型测试完成！")
