# -*- coding: utf-8 -*-

"""
测试generate_step1_predictions.py的修复
"""

import subprocess
import sys
import os

def test_generate_predictions_help():
    """测试generate_step1_predictions.py的帮助信息，确保参数解析正常"""
    print("测试generate_step1_predictions.py参数解析...")
    
    try:
        # 运行帮助命令
        result = subprocess.run([
            sys.executable, 'generate_step1_predictions.py', '--help'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✓ generate_step1_predictions.py参数解析正常")
            print("支持的GAT参数:")
            lines = result.stdout.split('\n')
            for line in lines:
                if 'gat' in line.lower():
                    print(f"  {line.strip()}")
            return True
        else:
            print(f"❌ 参数解析失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 命令超时")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_config_creation():
    """测试配置创建是否包含GAT参数"""
    print("\n测试配置创建...")
    
    try:
        # 导入相关模块
        from config import Config, get_model_config
        from models.step1_model import Step1Config
        
        # 创建配置
        config = Config()
        args = config.parse_args(['--dataset', 'iemocap', '--use_gat'])
        
        # 检查GAT参数
        gat_params = ['use_gat', 'gat_n_heads', 'gat_n_layers', 'gat_window_size', 
                     'gat_use_speaker_edges', 'gat_distance_decay']
        
        missing_params = []
        for param in gat_params:
            if not hasattr(args, param):
                missing_params.append(param)
        
        if missing_params:
            print(f"❌ 配置中缺少GAT参数: {missing_params}")
            return False
        
        # 测试模型配置创建
        model_config = get_model_config(args, 'step1')
        
        for param in gat_params:
            if not hasattr(model_config, param):
                missing_params.append(f"model_config.{param}")
        
        if missing_params:
            print(f"❌ 模型配置中缺少GAT参数: {missing_params}")
            return False
        
        print("✓ 配置创建正常，包含所有GAT参数")
        print(f"  use_gat: {model_config.use_gat}")
        print(f"  gat_n_heads: {model_config.gat_n_heads}")
        print(f"  gat_n_layers: {model_config.gat_n_layers}")
        return True
        
    except Exception as e:
        print(f"❌ 配置创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=== 测试generate_step1_predictions.py修复 ===")
    
    success = True
    
    # 测试参数解析
    if not test_generate_predictions_help():
        success = False
    
    # 测试配置创建
    if not test_config_creation():
        success = False
    
    if success:
        print("\n🎉 所有测试通过！generate_step1_predictions.py修复成功！")
        print("\n现在可以重新运行训练流程：")
        print("python run.py --dataset iemocap --model_type bilstm --use_gat")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
