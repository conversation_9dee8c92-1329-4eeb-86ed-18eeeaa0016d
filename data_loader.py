# -*- coding: utf-8 -*-

import h5py
import torch
import numpy as np
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer
import random
from collections import defaultdict
from model_path_utils import ensure_model_path_exists

class MECPEDataset(Dataset):
    """MECPE数据集加载器，支持IEMOCAP和MELD数据集"""

    def __init__(self, h5_file_path, dataset_type='iemocap', max_doc_len=35, max_sen_len=35,
                 tokenizer_name='../roberta', stage='step1', use_predicted_labels=False, pred_future_cause=True,
                 step1_pred_dir=None, use_emocate=False, use_emotion_category=True):
        """
        Args:
            h5_file_path: H5文件路径
            dataset_type: 数据集类型 ('iemocap' or 'meld')
            max_doc_len: 最大对话长度
            max_sen_len: 最大句子长度
            tokenizer_name: tokenizer名称
            stage: 训练阶段 ('step1' or 'step2')
            use_predicted_labels: Step2 是否使用 Step1 预测结果来筛选候选对
            pred_future_cause: 是否允许未来原因
            step1_pred_dir: Step1 预测结果目录（包含 train/dev/test 的JSON），使用时需要提供
            use_emocate: 是否使用细粒度情感分类（多类），否则使用二分类（neutral vs non-neutral）
            use_emotion_category: 是否使用情感类别作为Step2特征（应使用Step1预测，避免信息泄露）
        """
        self.h5_file_path = h5_file_path
        self.dataset_type = dataset_type
        self.max_doc_len = max_doc_len
        self.max_sen_len = max_sen_len
        self.stage = stage
        self.use_predicted_labels = use_predicted_labels
        self.pred_future_cause = pred_future_cause
        self.step1_pred_dir = step1_pred_dir
        self.use_emocate = use_emocate
        self.use_emotion_category = use_emotion_category

        # 加载tokenizer（使用本地路径）
        tokenizer_name = ensure_model_path_exists(tokenizer_name)
        self.tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)

        # 存储已警告过的会话，避免重复打印
        self._warned_missing_emocate = set()

        # 获取实际词汇表大小
        self.vocab_size = len(self.tokenizer)

        # 情感标签映射
        if dataset_type == 'iemocap':
            self.emotion_mapping = {
                'neutral': 0, 'anger': 1, 'fear': 2,
                'happiness': 3, 'sadness': 4, 'frustration': 5
            }
        else:  # meld
            self.emotion_mapping = {
                'neutral': 0, 'anger': 1, 'disgust': 2, 'fear': 3,
                'joy': 4, 'sadness': 5, 'surprise': 6
            }

        # 加载对话列表
        with h5py.File(h5_file_path, 'r') as f:
            self.conversation_keys = [k for k in f.keys() if k.startswith('conversation_')]

        self.data = self._load_data()

    def _load_data(self):
        """加载和预处理数据"""
        data = []

        # 如果是Step2且要求使用Step1预测结果，则先加载预测
        step1_preds = None
        if self.stage == 'step2' and self.use_predicted_labels and self.step1_pred_dir:
            step1_preds = self._load_step1_predictions()

        with h5py.File(self.h5_file_path, 'r') as f:
            for conv_key in self.conversation_keys:
                conv = f[conv_key]

                # 读取基本信息
                texts = [t.decode('utf-8') for t in conv['texts'][:]]
                speakers = [s.decode('utf-8') for s in conv['speakers'][:]]
                emotions = [e.decode('utf-8') for e in conv['emotions'][:]]

                # 处理多模态特征
                if self.dataset_type == 'iemocap':
                    audio_features = conv['audio_features'][:]  # (n_utterances, 100)
                    visual_features = conv['visual_features'][:]  # (n_utterances, 100)
                else:  # meld
                    audio_features = conv['audio_features'][:]  # (n_utterances, 6373)
                    visual_features = conv['visual_features'][:]  # (n_utterances, 4096)

                # 处理情感-原因对
                emotion_cause_pairs = []
                for pair_str in conv['emotion_cause_pairs'][:]:
                    pair_str = pair_str.decode('utf-8')
                    parts = pair_str.split('|')
                    if len(parts) == 2:
                        emo_info, cause_id = parts[0], int(parts[1])
                        emo_id = int(emo_info.split('_')[0])
                        emotion_cause_pairs.append((emo_id, cause_id))

                # 构建数据项，标准化会话ID为字符串，避免与预测文件键不一致
                raw_conv_id = conv.attrs.get('conversation_ID', conv_key)
                conv_id = raw_conv_id.decode('utf-8') if isinstance(raw_conv_id, bytes) else str(raw_conv_id)
                conv_key_str = conv_key.decode('utf-8') if isinstance(conv_key, bytes) else str(conv_key)
                data_item = {
                    'conversation_id': conv_id,
                    'conversation_key': conv_key_str,
                    'texts': texts,
                    'speakers': speakers,
                    'emotions': emotions,
                    'emotion_labels': [self.emotion_mapping.get(e, 0) for e in emotions],
                    'audio_features': audio_features,
                    'visual_features': visual_features,
                    'emotion_cause_pairs': emotion_cause_pairs
                }

                if self.stage == 'step1':
                    data.extend(self._prepare_step1_data(data_item))
                else:  # step2
                    # 如果提供了step1预测，则注入到data_item（尝试多种键匹配）
                    if step1_preds is not None:
                        pred = None
                        if conv_id in step1_preds:
                            pred = step1_preds[conv_id]
                        elif conv_key_str in step1_preds:
                            pred = step1_preds[conv_key_str]
                        # 有些数据集会话ID可能是纯数字，尝试去除可能的前后缀
                        if pred is None and conv_id.isdigit() and int(conv_id) != conv_id:
                            num_key = str(int(conv_id))
                            if num_key in step1_preds:
                                pred = step1_preds[num_key]
                        if pred is not None:
                            data_item['step1_pred'] = pred
                    data.extend(self._prepare_step2_data(data_item))

        return data
    def _load_step1_predictions(self):
        """加载Step1预测结果，返回：{conversation_id: {"emo_candidates": [ids], "cause_candidates": [ids], "emo_category_pred": [per-utt category id(optional)]}}。
        约定从 step1_pred_dir 中读取三个文件：train.json / dev.json / test.json，内部是列表或字典，每条包含会话ID与每个话语的情感/原因预测。
        实际格式允许如下两类之一：
        1) { conversation_id: { "pred_emo": [0/1,...], "pred_cause": [0/1,...], "pred_emocate": [0..K] (可选) } }
        2) [ {"conversation_id": xxx, "pred_emo": [...], "pred_cause": [...], "pred_emocate": [...]}, ...]
        """
        import os, json
        result = {}
        # 根据当前数据来源文件名推断 split
        split = 'train' if 'train' in self.h5_file_path else ('dev' if 'dev' in self.h5_file_path else 'test')
        file_path = os.path.join(self.step1_pred_dir, f'{split}.json')
        if not os.path.exists(file_path):
            print(f'[WARN] Step1预测文件不存在: {file_path}，将回退为不使用预测。')
            return None
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            print(f'[WARN] 读取Step1预测文件失败: {file_path}, err={e}，将回退为不使用预测。')
            return None

        if isinstance(data, dict):
            # 已是 {conv_id: {...}}
            for k, v in data.items():
                pred_emo = v.get('pred_emo', [])
                pred_cause = v.get('pred_cause', [])
                pred_emocate = v.get('pred_emocate', None)
                # 若缺少细粒度类别，回退为二分类预测，避免后续缺失
                if pred_emocate is None:
                    pred_emocate = pred_emo
                emo_candidates = [i+1 for i, x in enumerate(pred_emo) if int(x) == 1]
                cause_candidates = [i+1 for i, x in enumerate(pred_cause) if int(x) == 1]
                result[str(k)] = {
                    'emo_candidates': emo_candidates if emo_candidates else list(range(1, len(pred_emo)+1)),
                    'cause_candidates': cause_candidates if cause_candidates else list(range(1, len(pred_cause)+1)),
                    'emo_category_pred': pred_emocate
                }
        elif isinstance(data, list):
            for item in data:
                conv_id = item.get('conversation_id')
                if conv_id is None:
                    continue
                pred_emo = item.get('pred_emo', [])
                pred_cause = item.get('pred_cause', [])
                pred_emocate = item.get('pred_emocate', None)
                if pred_emocate is None:
                    pred_emocate = pred_emo
                emo_candidates = [i+1 for i, x in enumerate(pred_emo) if int(x) == 1]
                cause_candidates = [i+1 for i, x in enumerate(pred_cause) if int(x) == 1]
                result[conv_id] = {
                    'emo_candidates': emo_candidates if emo_candidates else list(range(1, len(pred_emo)+1)),
                    'cause_candidates': cause_candidates if cause_candidates else list(range(1, len(pred_cause)+1)),
                    'emo_category_pred': pred_emocate
                }
        else:
            print(f'[WARN] Step1预测文件格式无法解析: {type(data)}，将回退为不使用预测。')
            return None

        # 构建宽松匹配表：提取纯数字ID到预测的映射（例如 "dia_962" ↔ "962"）
        self._step1_pred_digit_map = {}
        for key, val in result.items():
            digits = ''.join(ch for ch in str(key) if ch.isdigit())
            if digits:
                self._step1_pred_digit_map.setdefault(digits, val)

        return result


    def _prepare_step1_data(self, data_item):
        """为Step1准备数据：情感和原因检测"""
        step1_data = []

        texts = data_item['texts']
        speakers = data_item['speakers']
        emotion_labels = data_item['emotion_labels']
        audio_features = data_item['audio_features']
        visual_features = data_item['visual_features']
        emotion_cause_pairs = data_item['emotion_cause_pairs']

        doc_len = min(len(texts), self.max_doc_len)

        # 构建情感和原因标签
        y_emotion = np.zeros(self.max_doc_len)
        y_cause = np.zeros(self.max_doc_len)

        # 设置情感标签
        for i in range(doc_len):
            if self.use_emocate:
                # 细粒度情感分类：使用原始情感类别ID
                y_emotion[i] = emotion_labels[i]
            else:
                # 二分类：neutral vs non-neutral
                y_emotion[i] = 1 if emotion_labels[i] > 0 else 0

        # 设置原因标签
        cause_utterances = set([pair[1] for pair in emotion_cause_pairs])
        for i in range(doc_len):
            y_cause[i] = 1 if (i + 1) in cause_utterances else 0

        # tokenize文本
        input_ids, attention_mask = self._tokenize_texts(texts)

        # 处理多模态特征
        padded_audio = self._pad_features(audio_features, doc_len)
        padded_visual = self._pad_features(visual_features, doc_len)

        step1_data.append({
            'input_ids': input_ids,
            'attention_mask': attention_mask,
            'audio_features': padded_audio,
            'visual_features': padded_visual,
            'y_emotion': y_emotion,
            'y_cause': y_cause,
            'doc_len': doc_len,
            'conversation_id': data_item['conversation_id'],
            'texts': texts,  # 添加原始文本，供BERT_doc使用
            'speakers': speakers  # 添加说话人信息，供GAT构图使用
        })

        return step1_data

    def _prepare_step2_data(self, data_item):
        """为Step2准备数据：情感-原因对提取"""
        step2_data = []

        texts = data_item['texts']
        emotion_labels = data_item['emotion_labels']
        audio_features = data_item['audio_features']
        visual_features = data_item['visual_features']
        emotion_cause_pairs = data_item['emotion_cause_pairs']

        doc_len = min(len(texts), self.max_doc_len)

        # 生成候选情感话语列表和候选原因话语列表
        if self.use_predicted_labels and 'step1_pred' in data_item:
            # 使用Step1预测结果生成候选对
            step1_pred = data_item['step1_pred']
            emotion_utterances = step1_pred.get('emo_candidates', [])
            cause_utterances = step1_pred.get('cause_candidates', [])

            # 确保候选话语在有效范围内
            emotion_utterances = [eid for eid in emotion_utterances if 1 <= eid <= doc_len]
            cause_utterances = [cid for cid in cause_utterances if 1 <= cid <= doc_len]

            # 如果Step1没有预测出任何候选，回退到所有话语
            if not emotion_utterances:
                emotion_utterances = list(range(1, doc_len + 1))
            if not cause_utterances:
                cause_utterances = list(range(1, doc_len + 1))

            # 获取Step1预测的情感类别（如果有）——兼容两种键名
            pred_emotion_categories = step1_pred.get('pred_emocate', None)
            if pred_emotion_categories is None:
                pred_emotion_categories = step1_pred.get('emo_category_pred', None)

        else:
            # 回退策略：使用所有话语作为候选（避免数据泄露的真实场景）
            emotion_utterances = list(range(1, doc_len + 1))
            cause_utterances = list(range(1, doc_len + 1))
            pred_emotion_categories = None

        # 生成候选对（只在情感话语和原因话语之间配对）
        for emo_id in emotion_utterances:
            for cause_id in cause_utterances:
                # 时间约束：如果不考虑未来原因，只配对过去的原因
                if not self.pred_future_cause:
                    if emo_id < cause_id:  # 情感在原因之前，跳过
                        continue

                # 构建候选对
                is_positive = (emo_id, cause_id) in emotion_cause_pairs

                # tokenize情感话语和原因话语的文本
                emo_text = texts[emo_id - 1]
                cause_text = texts[cause_id - 1]

                emo_input_ids = self.tokenizer(
                    emo_text, max_length=self.max_sen_len,
                    padding='max_length', truncation=True, return_tensors='pt'
                )
                cause_input_ids = self.tokenizer(
                    cause_text, max_length=self.max_sen_len,
                    padding='max_length', truncation=True, return_tensors='pt'
                )

                # 获取多模态特征
                emo_audio = audio_features[emo_id - 1]
                emo_visual = visual_features[emo_id - 1]
                cause_audio = audio_features[cause_id - 1]
                cause_visual = visual_features[cause_id - 1]

                # 决定是否使用情感类别特征
                if not self.use_emotion_category:
                    # 不使用情感类别特征
                    emotion_category = 0  # 使用默认值，模型会忽略此特征
                elif pred_emotion_categories is not None and len(pred_emotion_categories) > emo_id - 1:
                    # 使用Step1预测的情感类别（避免信息泄露）
                    emotion_category = pred_emotion_categories[emo_id - 1]
                else:
                    # 如果没有Step1预测结果，则不使用情感类别特征（避免信息泄露）
                    emotion_category = 0
                    if self.use_emotion_category:
                        print(f"警告: 对话 {data_item['conversation_id']} 缺少Step1预测的情感类别，将使用默认值0")

                step2_data.append({
                    'emo_input_ids': emo_input_ids['input_ids'].squeeze(0),
                    'emo_attention_mask': emo_input_ids['attention_mask'].squeeze(0),
                    'cause_input_ids': cause_input_ids['input_ids'].squeeze(0),
                    'cause_attention_mask': cause_input_ids['attention_mask'].squeeze(0),
                    'emo_audio': emo_audio,
                    'emo_visual': emo_visual,
                    'cause_audio': cause_audio,
                    'cause_visual': cause_visual,
                    'distance': cause_id - emo_id + 100,  # 原论文的距离编码方式
                    'emotion_category': emotion_category,
                    'label': 1 if is_positive else 0,
                    'conversation_id': data_item['conversation_id'],
                    'emo_id': emo_id,
                    'cause_id': cause_id
                })

        return step2_data

    def _tokenize_texts(self, texts):
        """对文本进行tokenization"""
        doc_len = min(len(texts), self.max_doc_len)

        # 为每个话语进行tokenization
        input_ids = torch.zeros(self.max_doc_len, self.max_sen_len, dtype=torch.long)
        attention_mask = torch.zeros(self.max_doc_len, self.max_sen_len, dtype=torch.long)

        for i in range(doc_len):
            encoded = self.tokenizer(
                texts[i], max_length=self.max_sen_len,
                padding='max_length', truncation=True, return_tensors='pt'
            )
            input_ids[i] = encoded['input_ids'].squeeze(0)
            attention_mask[i] = encoded['attention_mask'].squeeze(0)

        return input_ids, attention_mask

    def _pad_features(self, features, doc_len):
        """填充多模态特征"""
        feature_dim = features.shape[1]
        padded = np.zeros((self.max_doc_len, feature_dim))
        padded[:doc_len] = features[:doc_len]
        return torch.tensor(padded, dtype=torch.float32)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        return self.data[idx]


def create_data_loaders(dataset_name='iemocap', batch_size=8, stage='step1', use_predicted_labels=False, pred_future_cause=True, step1_pred_dir=None, use_emocate=False, use_emotion_category=True):
    """创建数据加载器"""

    if dataset_name == 'iemocap':
        train_path = 'data/iemocap/train.h5'
        test_path = 'data/iemocap/test.h5'
        dev_path = None
    else:  # meld
        train_path = 'data/meld/train.h5'
        test_path = 'data/meld/test.h5'
        dev_path = 'data/meld/dev.h5'

    # 创建数据集
    train_dataset = MECPEDataset(train_path, dataset_name, stage=stage,
                                use_predicted_labels=use_predicted_labels, pred_future_cause=pred_future_cause,
                                step1_pred_dir=step1_pred_dir, use_emocate=use_emocate,
                                use_emotion_category=use_emotion_category)
    test_dataset = MECPEDataset(test_path, dataset_name, stage=stage,
                               use_predicted_labels=use_predicted_labels, pred_future_cause=pred_future_cause,
                               step1_pred_dir=step1_pred_dir, use_emocate=use_emocate,
                               use_emotion_category=use_emotion_category)

    # 获取实际词汇表大小
    vocab_size = train_dataset.vocab_size

    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset, batch_size=batch_size, shuffle=True,
        collate_fn=collate_fn_step1 if stage == 'step1' else collate_fn_step2
    )
    test_loader = DataLoader(
        test_dataset, batch_size=batch_size, shuffle=False,
        collate_fn=collate_fn_step1 if stage == 'step1' else collate_fn_step2
    )

    dev_loader = None
    if dev_path:
        dev_dataset = MECPEDataset(dev_path, dataset_name, stage=stage,
                                   use_predicted_labels=use_predicted_labels, pred_future_cause=pred_future_cause,
                                   step1_pred_dir=step1_pred_dir, use_emocate=use_emocate,
                                   use_emotion_category=use_emotion_category)
        dev_loader = DataLoader(
            dev_dataset, batch_size=batch_size, shuffle=False,
            collate_fn=collate_fn_step1 if stage == 'step1' else collate_fn_step2
        )

    return train_loader, test_loader, dev_loader, vocab_size


def collate_fn_step1(batch):
    """Step1的批处理函数"""
    return {
        'input_ids': torch.stack([item['input_ids'] for item in batch]),
        'attention_mask': torch.stack([item['attention_mask'] for item in batch]),
        'audio_features': torch.stack([item['audio_features'] for item in batch]),
        'visual_features': torch.stack([item['visual_features'] for item in batch]),
        'y_emotion': torch.stack([torch.tensor(item['y_emotion']) for item in batch]),
        'y_cause': torch.stack([torch.tensor(item['y_cause']) for item in batch]),
        'doc_len': torch.tensor([item['doc_len'] for item in batch]),
        'conversation_id': [item['conversation_id'] for item in batch],
        'texts': [item['texts'] for item in batch],  # 原始文本列表
        'speakers': [item['speakers'] for item in batch]  # 说话人列表，用于构建图
    }


def collate_fn_step2(batch):
    """Step2的批处理函数"""
    return {
        'emo_input_ids': torch.stack([item['emo_input_ids'] for item in batch]),
        'emo_attention_mask': torch.stack([item['emo_attention_mask'] for item in batch]),
        'cause_input_ids': torch.stack([item['cause_input_ids'] for item in batch]),
        'cause_attention_mask': torch.stack([item['cause_attention_mask'] for item in batch]),
        'emo_audio': torch.stack([torch.tensor(item['emo_audio'], dtype=torch.float32) for item in batch]),
        'emo_visual': torch.stack([torch.tensor(item['emo_visual'], dtype=torch.float32) for item in batch]),
        'cause_audio': torch.stack([torch.tensor(item['cause_audio'], dtype=torch.float32) for item in batch]),
        'cause_visual': torch.stack([torch.tensor(item['cause_visual'], dtype=torch.float32) for item in batch]),
        'distance': torch.tensor([item['distance'] for item in batch]),
        'emotion_category': torch.tensor([item['emotion_category'] for item in batch]),
        'label': torch.tensor([item['label'] for item in batch], dtype=torch.long),
        'conversation_id': [item['conversation_id'] for item in batch],
        'emo_id': torch.tensor([item['emo_id'] for item in batch]),
        'cause_id': torch.tensor([item['cause_id'] for item in batch])
    }


if __name__ == "__main__":
    # 测试数据加载器
    print("测试IEMOCAP数据加载器...")
    train_loader, test_loader, _ = create_data_loaders('iemocap', batch_size=2, stage='step1')

    print(f"训练集批次数: {len(train_loader)}")
    print(f"测试集批次数: {len(test_loader)}")

    # 测试一个批次
    for batch in train_loader:
        print("Step1批次示例:")
        for key, value in batch.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape}")
            else:
                print(f"  {key}: {len(value) if isinstance(value, list) else value}")
        break

    print("\n测试Step2...")
    train_loader_s2, test_loader_s2, _ = create_data_loaders('iemocap', batch_size=2, stage='step2')

    for batch in train_loader_s2:
        print("Step2批次示例:")
        for key, value in batch.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape}")
            else:
                print(f"  {key}: {len(value) if isinstance(value, list) else value}")
        break
